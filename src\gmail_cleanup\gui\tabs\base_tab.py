"""
Base tab widget with common functionality.

All tab widgets should inherit from this base class.
"""

import logging
from typing import Optional

from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel
from PySide6.QtCore import Signal, QTimer
from PySide6.QtGui import QFont

from ...core.config import AppConfig

logger = logging.getLogger(__name__)


class BaseTab(QWidget):
    """Base class for all tab widgets."""
    
    # Common signals
    status_message = Signal(str)  # Emit to update status bar
    error_occurred = Signal(str, str)  # title, message
    warning_occurred = Signal(str, str)  # title, message
    info_occurred = Signal(str, str)  # title, message
    
    def __init__(self, config: AppConfig, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.config = config
        self.main_window = parent  # Reference to main window
        
        # Set up basic layout
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(20, 20, 20, 20)
        self.layout.setSpacing(15)
        
        # Initialize UI
        self._setup_ui()
        self._setup_connections()
        
        # Auto-refresh timer (can be overridden by subclasses)
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
    
    def _setup_ui(self) -> None:
        """Set up the UI. Override in subclasses."""
        # Default implementation - just a placeholder
        title = QLabel("Base Tab")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        self.layout.addWidget(title)
        
        placeholder = QLabel("This is a base tab. Override _setup_ui() in subclasses.")
        self.layout.addWidget(placeholder)
        
        self.layout.addStretch()
    
    def _setup_connections(self) -> None:
        """Set up signal connections. Override in subclasses."""
        # Connect common signals to main window if available
        if self.main_window:
            self.status_message.connect(
                lambda msg: self.main_window.status_bar.showMessage(msg)
            )
            self.error_occurred.connect(self.main_window.show_error)
            self.warning_occurred.connect(self.main_window.show_warning)
            self.info_occurred.connect(self.main_window.show_info)
    
    def refresh_data(self) -> None:
        """Refresh tab data. Override in subclasses."""
        logger.debug(f"Refreshing data for {self.__class__.__name__}")
    
    def on_tab_activated(self) -> None:
        """Called when this tab becomes active. Override in subclasses."""
        logger.debug(f"Tab activated: {self.__class__.__name__}")
        self.refresh_data()
    
    def on_tab_deactivated(self) -> None:
        """Called when this tab becomes inactive. Override in subclasses."""
        logger.debug(f"Tab deactivated: {self.__class__.__name__}")
    
    def start_auto_refresh(self, interval_ms: int = 30000) -> None:
        """Start automatic data refresh."""
        self.refresh_timer.start(interval_ms)
        logger.debug(f"Started auto-refresh for {self.__class__.__name__} (interval: {interval_ms}ms)")
    
    def stop_auto_refresh(self) -> None:
        """Stop automatic data refresh."""
        self.refresh_timer.stop()
        logger.debug(f"Stopped auto-refresh for {self.__class__.__name__}")
    
    def emit_status(self, message: str) -> None:
        """Emit a status message."""
        self.status_message.emit(message)
    
    def emit_error(self, title: str, message: str) -> None:
        """Emit an error message."""
        self.error_occurred.emit(title, message)
        logger.error(f"{title}: {message}")
    
    def emit_warning(self, title: str, message: str) -> None:
        """Emit a warning message."""
        self.warning_occurred.emit(title, message)
        logger.warning(f"{title}: {message}")
    
    def emit_info(self, title: str, message: str) -> None:
        """Emit an info message."""
        self.info_occurred.emit(title, message)
        logger.info(f"{title}: {message}")
    
    def is_authenticated(self) -> bool:
        """Check if user is authenticated with Gmail. Override in subclasses."""
        # This would typically check with an auth manager
        return False
    
    def require_authentication(self) -> bool:
        """Ensure user is authenticated. Return True if authenticated."""
        if not self.is_authenticated():
            self.emit_warning(
                "Authentication Required",
                "Please configure Gmail authentication in the Settings tab."
            )
            return False
        return True
