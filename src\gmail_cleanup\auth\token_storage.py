"""
Secure token storage for OAuth credentials.

Handles secure storage and retrieval of OAuth tokens using OS keyring when available.
"""

import json
import logging
from typing import Optional, Dict, Any
from pathlib import Path

try:
    import keyring
    KEYRING_AVAILABLE = True
except ImportError:
    KEYRING_AVAILABLE = False
    keyring = None

from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os

from ..core.config import AppConfig

logger = logging.getLogger(__name__)


class TokenStorage:
    """Manages secure storage of OAuth tokens."""
    
    SERVICE_NAME = "GmailCleanupTool"
    USERNAME = "oauth_credentials"
    
    def __init__(self, config: AppConfig):
        self.config = config
        self.credentials_file = config.get_data_dir() / "credentials.enc"
        self.key_file = config.get_data_dir() / "key.key"
        
        # Ensure data directory exists
        config.get_data_dir().mkdir(parents=True, exist_ok=True)
    
    def _get_encryption_key(self) -> bytes:
        """Get or create encryption key for file-based storage."""
        if self.key_file.exists():
            with open(self.key_file, 'rb') as f:
                return f.read()
        else:
            # Generate new key
            key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(key)
            
            # Set restrictive permissions on key file
            try:
                os.chmod(self.key_file, 0o600)
            except OSError:
                logger.warning("Could not set restrictive permissions on key file")
            
            return key
    
    def _encrypt_data(self, data: str) -> bytes:
        """Encrypt data for storage."""
        key = self._get_encryption_key()
        fernet = Fernet(key)
        return fernet.encrypt(data.encode())
    
    def _decrypt_data(self, encrypted_data: bytes) -> str:
        """Decrypt data from storage."""
        key = self._get_encryption_key()
        fernet = Fernet(key)
        return fernet.decrypt(encrypted_data).decode()
    
    def save_credentials(self, credentials: Dict[str, Any]) -> bool:
        """
        Save OAuth credentials securely.
        
        Args:
            credentials: Dictionary containing OAuth credential data
            
        Returns:
            True if saved successfully, False otherwise
        """
        try:
            credentials_json = json.dumps(credentials)
            
            # Try keyring first if available
            if KEYRING_AVAILABLE:
                try:
                    keyring.set_password(self.SERVICE_NAME, self.USERNAME, credentials_json)
                    logger.info("Saved credentials to system keyring")
                    
                    # Remove file-based storage if it exists
                    if self.credentials_file.exists():
                        self.credentials_file.unlink()
                        logger.debug("Removed old file-based credentials")
                    
                    return True
                except Exception as e:
                    logger.warning(f"Failed to save to keyring, falling back to file: {e}")
            
            # Fall back to encrypted file storage
            encrypted_data = self._encrypt_data(credentials_json)
            with open(self.credentials_file, 'wb') as f:
                f.write(encrypted_data)
            
            # Set restrictive permissions
            try:
                os.chmod(self.credentials_file, 0o600)
            except OSError:
                logger.warning("Could not set restrictive permissions on credentials file")
            
            logger.info("Saved credentials to encrypted file")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save credentials: {e}")
            return False
    
    def load_credentials(self) -> Optional[Dict[str, Any]]:
        """
        Load OAuth credentials from secure storage.
        
        Returns:
            Dictionary containing OAuth credential data, or None if not found
        """
        try:
            # Try keyring first if available
            if KEYRING_AVAILABLE:
                try:
                    credentials_json = keyring.get_password(self.SERVICE_NAME, self.USERNAME)
                    if credentials_json:
                        logger.info("Loaded credentials from system keyring")
                        return json.loads(credentials_json)
                except Exception as e:
                    logger.warning(f"Failed to load from keyring: {e}")
            
            # Try encrypted file storage
            if self.credentials_file.exists():
                with open(self.credentials_file, 'rb') as f:
                    encrypted_data = f.read()
                
                credentials_json = self._decrypt_data(encrypted_data)
                logger.info("Loaded credentials from encrypted file")
                return json.loads(credentials_json)
            
            logger.debug("No stored credentials found")
            return None
            
        except Exception as e:
            logger.error(f"Failed to load credentials: {e}")
            return None
    
    def clear_credentials(self) -> bool:
        """
        Clear stored OAuth credentials.
        
        Returns:
            True if cleared successfully, False otherwise
        """
        success = True
        
        try:
            # Clear from keyring if available
            if KEYRING_AVAILABLE:
                try:
                    keyring.delete_password(self.SERVICE_NAME, self.USERNAME)
                    logger.info("Cleared credentials from system keyring")
                except keyring.errors.PasswordDeleteError:
                    logger.debug("No credentials found in keyring to clear")
                except Exception as e:
                    logger.warning(f"Failed to clear keyring credentials: {e}")
                    success = False
            
            # Clear encrypted file
            if self.credentials_file.exists():
                self.credentials_file.unlink()
                logger.info("Cleared credentials file")
            
            # Clear encryption key
            if self.key_file.exists():
                self.key_file.unlink()
                logger.debug("Cleared encryption key file")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to clear credentials: {e}")
            return False
    
    def has_credentials(self) -> bool:
        """
        Check if credentials are stored.
        
        Returns:
            True if credentials are available, False otherwise
        """
        # Check keyring
        if KEYRING_AVAILABLE:
            try:
                credentials = keyring.get_password(self.SERVICE_NAME, self.USERNAME)
                if credentials:
                    return True
            except Exception:
                pass
        
        # Check file
        return self.credentials_file.exists()
    
    def get_storage_info(self) -> Dict[str, Any]:
        """
        Get information about credential storage.
        
        Returns:
            Dictionary with storage information
        """
        info = {
            "keyring_available": KEYRING_AVAILABLE,
            "keyring_backend": None,
            "file_storage_path": str(self.credentials_file),
            "has_keyring_credentials": False,
            "has_file_credentials": False,
        }
        
        if KEYRING_AVAILABLE:
            try:
                info["keyring_backend"] = keyring.get_keyring().__class__.__name__
                credentials = keyring.get_password(self.SERVICE_NAME, self.USERNAME)
                info["has_keyring_credentials"] = credentials is not None
            except Exception:
                pass
        
        info["has_file_credentials"] = self.credentials_file.exists()
        
        return info
