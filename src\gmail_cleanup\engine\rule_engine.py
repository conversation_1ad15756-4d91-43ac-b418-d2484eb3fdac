"""
Rule engine for evaluating cleanup criteria against email messages.

Provides the core logic for determining which messages match cleanup rules.
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from email.utils import parseaddr

from ..data.models import (
    EmailMessage, FilterCriterion, FilterRule, SafetySettings,
    CleanupCandidate, FilterField, FilterOperator, CleanupAction
)

logger = logging.getLogger(__name__)


class RuleEngine:
    """Evaluates cleanup rules against email messages."""
    
    def __init__(self, safety_settings: SafetySettings):
        """
        Initialize rule engine.
        
        Args:
            safety_settings: Safety configuration
        """
        self.safety_settings = safety_settings
        
        # Compile protected terms regex for performance
        self._protected_terms_regex = None
        if safety_settings.protected_terms:
            pattern = '|'.join(re.escape(term.lower()) for term in safety_settings.protected_terms)
            self._protected_terms_regex = re.compile(pattern, re.IGNORECASE)
    
    def evaluate_criterion(self, message: EmailMessage, criterion: FilterCriterion) -> bool:
        """
        Evaluate a single filter criterion against a message.
        
        Args:
            message: Email message to evaluate
            criterion: Filter criterion to apply
            
        Returns:
            True if message matches criterion, False otherwise
        """
        try:
            if criterion.field == FilterField.SENDER:
                return self._evaluate_sender(message, criterion)
            elif criterion.field == FilterField.SUBJECT:
                return self._evaluate_subject(message, criterion)
            elif criterion.field == FilterField.DATE:
                return self._evaluate_date(message, criterion)
            elif criterion.field == FilterField.SIZE:
                return self._evaluate_size(message, criterion)
            elif criterion.field == FilterField.LABEL:
                return self._evaluate_label(message, criterion)
            elif criterion.field == FilterField.CATEGORY:
                return self._evaluate_category(message, criterion)
            elif criterion.field == FilterField.HEADER:
                return self._evaluate_header(message, criterion)
            elif criterion.field == FilterField.STATUS:
                return self._evaluate_status(message, criterion)
            elif criterion.field == FilterField.THREAD_SIZE:
                return self._evaluate_thread_size(message, criterion)
            else:
                logger.warning(f"Unknown filter field: {criterion.field}")
                return False
        except Exception as e:
            logger.error(f"Error evaluating criterion {criterion.field}: {e}")
            return False
    
    def _evaluate_sender(self, message: EmailMessage, criterion: FilterCriterion) -> bool:
        """Evaluate sender-based criteria."""
        sender = message.sender.lower() if not criterion.case_sensitive else message.sender
        value = str(criterion.value).lower() if not criterion.case_sensitive else str(criterion.value)
        
        # Extract email address from sender field
        _, email_addr = parseaddr(sender)
        if not email_addr:
            email_addr = sender
        
        if criterion.operator == FilterOperator.EQUALS:
            return email_addr == value
        elif criterion.operator == FilterOperator.CONTAINS:
            return value in email_addr
        elif criterion.operator == FilterOperator.STARTS_WITH:
            return email_addr.startswith(value)
        elif criterion.operator == FilterOperator.ENDS_WITH:
            return email_addr.endswith(value)
        elif criterion.operator == FilterOperator.REGEX:
            try:
                flags = 0 if criterion.case_sensitive else re.IGNORECASE
                return bool(re.search(value, email_addr, flags))
            except re.error:
                logger.warning(f"Invalid regex pattern: {value}")
                return False
        elif criterion.operator == FilterOperator.IN_LIST:
            if isinstance(criterion.value, list):
                return any(item.lower() in email_addr for item in criterion.value)
            return False
        elif criterion.operator == FilterOperator.NOT_IN_LIST:
            if isinstance(criterion.value, list):
                return not any(item.lower() in email_addr for item in criterion.value)
            return True
        
        return False
    
    def _evaluate_subject(self, message: EmailMessage, criterion: FilterCriterion) -> bool:
        """Evaluate subject-based criteria."""
        subject = message.subject.lower() if not criterion.case_sensitive else message.subject
        value = str(criterion.value).lower() if not criterion.case_sensitive else str(criterion.value)
        
        if criterion.operator == FilterOperator.EQUALS:
            return subject == value
        elif criterion.operator == FilterOperator.CONTAINS:
            return value in subject
        elif criterion.operator == FilterOperator.STARTS_WITH:
            return subject.startswith(value)
        elif criterion.operator == FilterOperator.ENDS_WITH:
            return subject.endswith(value)
        elif criterion.operator == FilterOperator.REGEX:
            try:
                flags = 0 if criterion.case_sensitive else re.IGNORECASE
                return bool(re.search(value, subject, flags))
            except re.error:
                logger.warning(f"Invalid regex pattern: {value}")
                return False
        elif criterion.operator == FilterOperator.IN_LIST:
            if isinstance(criterion.value, list):
                return any(item.lower() in subject for item in criterion.value)
            return False
        elif criterion.operator == FilterOperator.NOT_IN_LIST:
            if isinstance(criterion.value, list):
                return not any(item.lower() in subject for item in criterion.value)
            return True
        
        return False
    
    def _evaluate_date(self, message: EmailMessage, criterion: FilterCriterion) -> bool:
        """Evaluate date-based criteria."""
        message_date = message.date
        
        if criterion.operator == FilterOperator.GREATER_THAN:
            # Parse relative date values
            if isinstance(criterion.value, str):
                if criterion.value.endswith('d'):
                    days = int(criterion.value[:-1])
                    cutoff_date = datetime.now() - timedelta(days=days)
                    return message_date < cutoff_date
                elif criterion.value.endswith('m'):
                    months = int(criterion.value[:-1])
                    cutoff_date = datetime.now() - timedelta(days=months * 30)
                    return message_date < cutoff_date
                elif criterion.value.endswith('y'):
                    years = int(criterion.value[:-1])
                    cutoff_date = datetime.now() - timedelta(days=years * 365)
                    return message_date < cutoff_date
                else:
                    # Try to parse as ISO date
                    try:
                        cutoff_date = datetime.fromisoformat(criterion.value)
                        return message_date < cutoff_date
                    except ValueError:
                        logger.warning(f"Invalid date format: {criterion.value}")
                        return False
        elif criterion.operator == FilterOperator.LESS_THAN:
            if isinstance(criterion.value, str):
                try:
                    cutoff_date = datetime.fromisoformat(criterion.value)
                    return message_date > cutoff_date
                except ValueError:
                    logger.warning(f"Invalid date format: {criterion.value}")
                    return False
        
        return False
    
    def _evaluate_size(self, message: EmailMessage, criterion: FilterCriterion) -> bool:
        """Evaluate size-based criteria."""
        message_size = message.size_bytes
        
        if criterion.operator == FilterOperator.GREATER_THAN:
            threshold = int(criterion.value)
            return message_size > threshold
        elif criterion.operator == FilterOperator.LESS_THAN:
            threshold = int(criterion.value)
            return message_size < threshold
        
        return False
    
    def _evaluate_label(self, message: EmailMessage, criterion: FilterCriterion) -> bool:
        """Evaluate label-based criteria."""
        message_labels = [label.lower() for label in message.labels]
        value = str(criterion.value).lower()
        
        if criterion.operator == FilterOperator.EQUALS:
            return value in message_labels
        elif criterion.operator == FilterOperator.NOT_IN_LIST:
            return value not in message_labels
        elif criterion.operator == FilterOperator.IN_LIST:
            if isinstance(criterion.value, list):
                return any(label.lower() in message_labels for label in criterion.value)
            return False
        
        return False
    
    def _evaluate_category(self, message: EmailMessage, criterion: FilterCriterion) -> bool:
        """Evaluate category-based criteria."""
        message_category = message.category
        value = str(criterion.value).lower()
        
        if criterion.operator == FilterOperator.EQUALS:
            return message_category and message_category.lower() == value
        elif criterion.operator == FilterOperator.IN_LIST:
            if isinstance(criterion.value, list):
                return message_category and message_category.lower() in [c.lower() for c in criterion.value]
            return False
        
        return False
    
    def _evaluate_header(self, message: EmailMessage, criterion: FilterCriterion) -> bool:
        """Evaluate header-based criteria."""
        # criterion.value should be in format "header_name:header_value"
        if ':' not in str(criterion.value):
            return False
        
        header_name, header_value = str(criterion.value).split(':', 1)
        header_name = header_name.lower().strip()
        header_value = header_value.lower().strip()
        
        message_header_value = message.headers.get(header_name, '').lower()
        
        if criterion.operator == FilterOperator.EQUALS:
            return message_header_value == header_value
        elif criterion.operator == FilterOperator.CONTAINS:
            return header_value in message_header_value
        
        return False
    
    def _evaluate_status(self, message: EmailMessage, criterion: FilterCriterion) -> bool:
        """Evaluate status-based criteria."""
        value = str(criterion.value).lower()
        
        if value == 'unread':
            return message.is_unread
        elif value == 'read':
            return not message.is_unread
        elif value == 'starred':
            return message.is_starred
        elif value == 'important':
            return message.is_important
        
        return False
    
    def _evaluate_thread_size(self, message: EmailMessage, criterion: FilterCriterion) -> bool:
        """Evaluate thread size criteria (placeholder - would need thread info)."""
        # This would require additional thread information
        # For now, return False as we don't have thread size data
        logger.warning("Thread size evaluation not implemented")
        return False
    
    def evaluate_rule(self, message: EmailMessage, rule: FilterRule) -> Tuple[bool, List[str]]:
        """
        Evaluate a complete rule against a message.
        
        Args:
            message: Email message to evaluate
            rule: Filter rule to apply
            
        Returns:
            Tuple of (matches, reasons) where reasons are human-readable explanations
        """
        if not rule.criteria:
            return False, []
        
        results = []
        reasons = []
        
        for criterion in rule.criteria:
            matches = self.evaluate_criterion(message, criterion)
            results.append(matches)
            
            if matches:
                reason = self._format_criterion_reason(criterion)
                reasons.append(reason)
        
        # Apply logic (AND/OR)
        if rule.logic.upper() == 'OR':
            final_result = any(results)
        else:  # Default to AND
            final_result = all(results)
        
        return final_result, reasons if final_result else []
    
    def _format_criterion_reason(self, criterion: FilterCriterion) -> str:
        """Format a human-readable reason for why a criterion matched."""
        field_names = {
            FilterField.SENDER: "sender",
            FilterField.SUBJECT: "subject",
            FilterField.DATE: "date",
            FilterField.SIZE: "size",
            FilterField.LABEL: "label",
            FilterField.CATEGORY: "category",
            FilterField.HEADER: "header",
            FilterField.STATUS: "status"
        }
        
        operator_names = {
            FilterOperator.EQUALS: "equals",
            FilterOperator.CONTAINS: "contains",
            FilterOperator.STARTS_WITH: "starts with",
            FilterOperator.ENDS_WITH: "ends with",
            FilterOperator.REGEX: "matches pattern",
            FilterOperator.GREATER_THAN: "greater than",
            FilterOperator.LESS_THAN: "less than",
            FilterOperator.IN_LIST: "in list",
            FilterOperator.NOT_IN_LIST: "not in list"
        }
        
        field_name = field_names.get(criterion.field, str(criterion.field))
        operator_name = operator_names.get(criterion.operator, str(criterion.operator))
        
        return f"{field_name} {operator_name} '{criterion.value}'"

    def apply_safety_checks(self, message: EmailMessage) -> Tuple[bool, List[str]]:
        """
        Apply safety checks to determine if a message should be protected.

        Args:
            message: Email message to check

        Returns:
            Tuple of (is_protected, protection_reasons)
        """
        protection_reasons = []

        # Check starred messages
        if self.safety_settings.skip_starred and message.is_starred:
            protection_reasons.append("Message is starred")

        # Check important messages
        if self.safety_settings.skip_important and message.is_important:
            protection_reasons.append("Message is marked as important")

        # Check protected terms in subject and snippet
        if self._protected_terms_regex:
            text_to_check = f"{message.subject} {message.snippet}".lower()
            if self._protected_terms_regex.search(text_to_check):
                protection_reasons.append("Contains protected terms")

        # Check sender allowlist
        if self.safety_settings.allowlist_domains:
            sender_email = self._extract_email_domain(message.sender)
            if sender_email and any(domain.lower() in sender_email.lower()
                                  for domain in self.safety_settings.allowlist_domains):
                protection_reasons.append("Sender is in allowlist")

        # Check sender blocklist (this actually makes it a candidate, not protected)
        # We'll handle this separately in the main evaluation

        return len(protection_reasons) > 0, protection_reasons

    def _extract_email_domain(self, sender: str) -> Optional[str]:
        """Extract domain from sender email address."""
        try:
            _, email_addr = parseaddr(sender)
            if '@' in email_addr:
                return email_addr.split('@')[1]
        except Exception:
            pass
        return None

    def is_sender_blocked(self, message: EmailMessage) -> bool:
        """Check if sender is in blocklist."""
        if not self.safety_settings.blocklist_senders:
            return False

        sender_email = message.sender.lower()
        for blocked in self.safety_settings.blocklist_senders:
            if blocked.lower() in sender_email:
                return True

        return False

    def evaluate_message(self, message: EmailMessage, rules: List[FilterRule]) -> CleanupCandidate:
        """
        Evaluate a message against all rules and safety checks.

        Args:
            message: Email message to evaluate
            rules: List of filter rules to apply

        Returns:
            CleanupCandidate with evaluation results
        """
        # First apply safety checks
        is_protected, protection_reasons = self.apply_safety_checks(message)

        if is_protected:
            # Message is protected, exclude it
            return CleanupCandidate(
                message=message,
                matched_rules=[],
                reasons=protection_reasons,
                action=CleanupAction.TRASH,  # Default action, but excluded
                excluded=True
            )

        # Check if sender is blocked (makes it a strong candidate)
        is_blocked = self.is_sender_blocked(message)

        # Evaluate against rules
        matched_rules = []
        all_reasons = []

        for rule in rules:
            matches, reasons = self.evaluate_rule(message, rule)
            if matches:
                matched_rules.append(rule.name or f"Rule {len(matched_rules) + 1}")
                all_reasons.extend(reasons)

        # Add blocklist reason if applicable
        if is_blocked:
            all_reasons.append("Sender is in blocklist")
            matched_rules.append("Blocklist")

        # Determine if this is a cleanup candidate
        is_candidate = len(matched_rules) > 0

        return CleanupCandidate(
            message=message,
            matched_rules=matched_rules,
            reasons=all_reasons,
            action=CleanupAction.TRASH,  # Default action
            excluded=not is_candidate
        )
