"""
Data models for the Gmail Cleanup Tool.

Defines the structure for email messages, rules, profiles, and run history.
"""

import json
from dataclasses import dataclass, field, asdict
from datetime import datetime
from typing import List, Dict, Any, Optional, Union
from enum import Enum


class FilterOperator(Enum):
    """Operators for filter criteria."""
    EQUALS = "equals"
    CONTAINS = "contains"
    STARTS_WITH = "starts_with"
    ENDS_WITH = "ends_with"
    REGEX = "regex"
    GREATER_THAN = "greater_than"
    LESS_THAN = "less_than"
    IN_LIST = "in_list"
    NOT_IN_LIST = "not_in_list"


class FilterField(Enum):
    """Fields that can be filtered on."""
    SENDER = "sender"
    SUBJECT = "subject"
    DATE = "date"
    SIZE = "size"
    LABEL = "label"
    CATEGORY = "category"
    HEADER = "header"
    STATUS = "status"
    THREAD_SIZE = "thread_size"


class CleanupAction(Enum):
    """Available cleanup actions."""
    LABEL_ONLY = "label_only"
    ARCHIVE = "archive"
    TRASH = "trash"
    DELETE_PERMANENT = "delete_permanent"


class RunStatus(Enum):
    """Status of a cleanup run."""
    PREVIEW = "preview"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class FilterCriterion:
    """A single filter criterion."""
    field: FilterField
    operator: FilterOperator
    value: Union[str, int, float, List[str]]
    case_sensitive: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "field": self.field.value,
            "operator": self.operator.value,
            "value": self.value,
            "case_sensitive": self.case_sensitive
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FilterCriterion':
        """Create from dictionary."""
        return cls(
            field=FilterField(data["field"]),
            operator=FilterOperator(data["operator"]),
            value=data["value"],
            case_sensitive=data.get("case_sensitive", False)
        )


@dataclass
class FilterRule:
    """A filter rule with multiple criteria."""
    criteria: List[FilterCriterion] = field(default_factory=list)
    logic: str = "AND"  # "AND" or "OR"
    name: str = ""
    description: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "criteria": [c.to_dict() for c in self.criteria],
            "logic": self.logic,
            "name": self.name,
            "description": self.description
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FilterRule':
        """Create from dictionary."""
        return cls(
            criteria=[FilterCriterion.from_dict(c) for c in data.get("criteria", [])],
            logic=data.get("logic", "AND"),
            name=data.get("name", ""),
            description=data.get("description", "")
        )


@dataclass
class SafetySettings:
    """Safety settings for cleanup operations."""
    skip_starred: bool = True
    skip_important: bool = True
    skip_from_contacts: bool = True
    thread_mode: str = "message"  # "message" or "thread"
    protected_terms: List[str] = field(default_factory=lambda: [
        "invoice", "receipt", "statement", "tax", "insurance",
        "order #", "shipping", "reservation", "boarding pass",
        "verification code", "password reset", "calendar invitation",
        "school", "daycare", "legal", "financial"
    ])
    allowlist_domains: List[str] = field(default_factory=list)
    blocklist_senders: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SafetySettings':
        """Create from dictionary."""
        return cls(**data)


@dataclass
class CleanupProfile:
    """A complete cleanup profile with rules and settings."""
    name: str
    description: str = ""
    rules: List[FilterRule] = field(default_factory=list)
    safety_settings: SafetySettings = field(default_factory=SafetySettings)
    action: CleanupAction = CleanupAction.TRASH
    size_filter_mb: Optional[float] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "name": self.name,
            "description": self.description,
            "rules": [r.to_dict() for r in self.rules],
            "safety_settings": self.safety_settings.to_dict(),
            "action": self.action.value,
            "size_filter_mb": self.size_filter_mb,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CleanupProfile':
        """Create from dictionary."""
        return cls(
            name=data["name"],
            description=data.get("description", ""),
            rules=[FilterRule.from_dict(r) for r in data.get("rules", [])],
            safety_settings=SafetySettings.from_dict(data.get("safety_settings", {})),
            action=CleanupAction(data.get("action", "trash")),
            size_filter_mb=data.get("size_filter_mb"),
            created_at=datetime.fromisoformat(data.get("created_at", datetime.now().isoformat())),
            updated_at=datetime.fromisoformat(data.get("updated_at", datetime.now().isoformat()))
        )


@dataclass
class EmailMessage:
    """Represents an email message."""
    message_id: str
    thread_id: str
    subject: str
    sender: str
    date: datetime
    size_bytes: int
    labels: List[str] = field(default_factory=list)
    category: Optional[str] = None
    snippet: str = ""
    headers: Dict[str, str] = field(default_factory=dict)
    is_starred: bool = False
    is_important: bool = False
    is_unread: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "message_id": self.message_id,
            "thread_id": self.thread_id,
            "subject": self.subject,
            "sender": self.sender,
            "date": self.date.isoformat(),
            "size_bytes": self.size_bytes,
            "labels": self.labels,
            "category": self.category,
            "snippet": self.snippet,
            "headers": self.headers,
            "is_starred": self.is_starred,
            "is_important": self.is_important,
            "is_unread": self.is_unread
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EmailMessage':
        """Create from dictionary."""
        return cls(
            message_id=data["message_id"],
            thread_id=data["thread_id"],
            subject=data["subject"],
            sender=data["sender"],
            date=datetime.fromisoformat(data["date"]),
            size_bytes=data["size_bytes"],
            labels=data.get("labels", []),
            category=data.get("category"),
            snippet=data.get("snippet", ""),
            headers=data.get("headers", {}),
            is_starred=data.get("is_starred", False),
            is_important=data.get("is_important", False),
            is_unread=data.get("is_unread", False)
        )


@dataclass
class CleanupCandidate:
    """A message candidate for cleanup with reasons."""
    message: EmailMessage
    matched_rules: List[str] = field(default_factory=list)
    reasons: List[str] = field(default_factory=list)
    action: CleanupAction = CleanupAction.TRASH
    excluded: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "message": self.message.to_dict(),
            "matched_rules": self.matched_rules,
            "reasons": self.reasons,
            "action": self.action.value,
            "excluded": self.excluded
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CleanupCandidate':
        """Create from dictionary."""
        return cls(
            message=EmailMessage.from_dict(data["message"]),
            matched_rules=data.get("matched_rules", []),
            reasons=data.get("reasons", []),
            action=CleanupAction(data.get("action", "trash")),
            excluded=data.get("excluded", False)
        )


@dataclass
class CleanupRun:
    """Represents a cleanup run (preview or actual)."""
    run_id: str
    profile_name: str
    status: RunStatus
    action: CleanupAction
    started_at: datetime
    completed_at: Optional[datetime] = None
    total_candidates: int = 0
    processed_count: int = 0
    success_count: int = 0
    error_count: int = 0
    estimated_space_mb: float = 0.0
    actual_space_mb: float = 0.0
    error_messages: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "run_id": self.run_id,
            "profile_name": self.profile_name,
            "status": self.status.value,
            "action": self.action.value,
            "started_at": self.started_at.isoformat(),
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "total_candidates": self.total_candidates,
            "processed_count": self.processed_count,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "estimated_space_mb": self.estimated_space_mb,
            "actual_space_mb": self.actual_space_mb,
            "error_messages": self.error_messages
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CleanupRun':
        """Create from dictionary."""
        return cls(
            run_id=data["run_id"],
            profile_name=data["profile_name"],
            status=RunStatus(data["status"]),
            action=CleanupAction(data["action"]),
            started_at=datetime.fromisoformat(data["started_at"]),
            completed_at=datetime.fromisoformat(data["completed_at"]) if data.get("completed_at") else None,
            total_candidates=data.get("total_candidates", 0),
            processed_count=data.get("processed_count", 0),
            success_count=data.get("success_count", 0),
            error_count=data.get("error_count", 0),
            estimated_space_mb=data.get("estimated_space_mb", 0.0),
            actual_space_mb=data.get("actual_space_mb", 0.0),
            error_messages=data.get("error_messages", [])
        )
