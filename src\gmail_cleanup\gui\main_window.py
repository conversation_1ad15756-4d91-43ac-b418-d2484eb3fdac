"""
Main window for the Gmail Cleanup Tool.

Provides the primary interface with sidebar navigation and tab management.
"""

import logging
from typing import Optional

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, QSplitter,
    QListWidget, QListWidgetItem, QStackedWidget, QLabel,
    QMessageBox, QStatusBar
)
from PySide6.QtCore import Qt, QSize, Signal
from PySide6.QtGui import QIcon, QFont

from ..core.config import AppConfig
from .tabs.dashboard import DashboardTab
from .tabs.criteria_builder import CriteriaBuilderTab
from .tabs.preview import PreviewTab
from .tabs.cleanup import CleanupTab
from .tabs.backups_reports import BackupsReportsTab
from .tabs.settings import SettingsTab
from .tabs.logs import LogsTab

logger = logging.getLogger(__name__)


class MainWindow(QMainWindow):
    """Main application window with sidebar navigation."""
    
    # Signals
    tab_changed = Signal(str)  # Emitted when tab changes
    
    def __init__(self, config: AppConfig, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.config = config
        self.current_tab = "dashboard"
        
        # Initialize UI
        self._setup_ui()
        self._setup_tabs()
        self._setup_connections()
        self._restore_geometry()
        
        logger.info("Main window initialized")
    
    def _setup_ui(self) -> None:
        """Set up the main UI layout."""
        self.setWindowTitle("Gmail Cleanup Tool")
        self.setMinimumSize(1200, 800)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Create splitter for resizable sidebar
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # Create sidebar
        self.sidebar = self._create_sidebar()
        splitter.addWidget(self.sidebar)
        
        # Create main content area
        self.content_stack = QStackedWidget()
        splitter.addWidget(self.content_stack)
        
        # Set splitter proportions (sidebar: content = 1:4)
        splitter.setSizes([250, 950])
        splitter.setCollapsible(0, False)  # Don't allow sidebar to collapse
        
        # Create status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")
    
    def _create_sidebar(self) -> QWidget:
        """Create the sidebar with navigation items."""
        sidebar_widget = QWidget()
        sidebar_widget.setFixedWidth(250)
        sidebar_widget.setStyleSheet("""
            QWidget {
                background-color: #f5f5f5;
                border-right: 1px solid #ddd;
            }
        """)
        
        layout = QVBoxLayout(sidebar_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # App title
        title_label = QLabel("Gmail Cleanup Tool")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                padding: 20px;
                background-color: #e0e0e0;
                border-bottom: 1px solid #ccc;
            }
        """)
        layout.addWidget(title_label)
        
        # Navigation list
        self.nav_list = QListWidget()
        self.nav_list.setStyleSheet("""
            QListWidget {
                border: none;
                background-color: transparent;
                outline: none;
            }
            QListWidget::item {
                padding: 15px 20px;
                border-bottom: 1px solid #eee;
                font-size: 14px;
            }
            QListWidget::item:selected {
                background-color: #007acc;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #e6f3ff;
            }
        """)
        
        # Add navigation items
        nav_items = [
            ("dashboard", "📊 Dashboard"),
            ("criteria", "🔧 Criteria Builder"),
            ("preview", "👁️ Preview"),
            ("cleanup", "🧹 Cleanup"),
            ("backups", "💾 Backups & Reports"),
            ("settings", "⚙️ Settings"),
            ("logs", "📋 Logs"),
        ]
        
        for item_id, item_text in nav_items:
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, item_id)
            self.nav_list.addItem(item)
        
        # Select first item
        self.nav_list.setCurrentRow(0)
        
        layout.addWidget(self.nav_list)
        layout.addStretch()  # Push items to top
        
        return sidebar_widget
    
    def _setup_tabs(self) -> None:
        """Initialize all tab widgets."""
        # Create tab instances
        self.tabs = {
            "dashboard": DashboardTab(self.config, self),
            "criteria": CriteriaBuilderTab(self.config, self),
            "preview": PreviewTab(self.config, self),
            "cleanup": CleanupTab(self.config, self),
            "backups": BackupsReportsTab(self.config, self),
            "settings": SettingsTab(self.config, self),
            "logs": LogsTab(self.config, self),
        }
        
        # Add tabs to stack
        for tab_id, tab_widget in self.tabs.items():
            self.content_stack.addWidget(tab_widget)
        
        # Show initial tab
        self._switch_to_tab("dashboard")
    
    def _setup_connections(self) -> None:
        """Set up signal connections."""
        self.nav_list.currentItemChanged.connect(self._on_nav_selection_changed)
        
        # Connect tab-specific signals if needed
        # Example: self.tabs["dashboard"].some_signal.connect(self._handle_signal)
    
    def _on_nav_selection_changed(self, current: QListWidgetItem, previous: QListWidgetItem) -> None:
        """Handle navigation selection change."""
        if current:
            tab_id = current.data(Qt.UserRole)
            self._switch_to_tab(tab_id)
    
    def _switch_to_tab(self, tab_id: str) -> None:
        """Switch to the specified tab."""
        if tab_id in self.tabs:
            self.content_stack.setCurrentWidget(self.tabs[tab_id])
            self.current_tab = tab_id
            self.tab_changed.emit(tab_id)
            
            # Update status bar
            tab_names = {
                "dashboard": "Dashboard",
                "criteria": "Criteria Builder",
                "preview": "Preview",
                "cleanup": "Cleanup",
                "backups": "Backups & Reports",
                "settings": "Settings",
                "logs": "Logs",
            }
            self.status_bar.showMessage(f"Current tab: {tab_names.get(tab_id, tab_id)}")
            
            logger.debug(f"Switched to tab: {tab_id}")
    
    def _restore_geometry(self) -> None:
        """Restore window geometry from configuration."""
        geometry = self.config.get("ui.window_geometry")
        state = self.config.get("ui.window_state")
        
        if geometry:
            self.restoreGeometry(geometry)
        if state:
            self.restoreState(state)
    
    def closeEvent(self, event) -> None:
        """Handle window close event."""
        # Save window geometry
        self.config.set("ui.window_geometry", self.saveGeometry())
        self.config.set("ui.window_state", self.saveState())
        
        # Check if any operations are in progress
        cleanup_tab = self.tabs.get("cleanup")
        if cleanup_tab and hasattr(cleanup_tab, 'is_operation_in_progress') and cleanup_tab.is_operation_in_progress():
            reply = QMessageBox.question(
                self,
                "Operation in Progress",
                "A cleanup operation is currently in progress. Are you sure you want to exit?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.No:
                event.ignore()
                return
        
        logger.info("Application closing")
        event.accept()
    
    def show_error(self, title: str, message: str) -> None:
        """Show an error dialog."""
        QMessageBox.critical(self, title, message)
    
    def show_warning(self, title: str, message: str) -> None:
        """Show a warning dialog."""
        QMessageBox.warning(self, title, message)
    
    def show_info(self, title: str, message: str) -> None:
        """Show an info dialog."""
        QMessageBox.information(self, title, message)
    
    def get_current_tab(self) -> str:
        """Get the currently active tab ID."""
        return self.current_tab
    
    def switch_to_tab(self, tab_id: str) -> None:
        """Public method to switch tabs programmatically."""
        # Find and select the corresponding nav item
        for i in range(self.nav_list.count()):
            item = self.nav_list.item(i)
            if item.data(Qt.UserRole) == tab_id:
                self.nav_list.setCurrentItem(item)
                break
