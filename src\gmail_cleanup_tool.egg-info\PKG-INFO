Metadata-Version: 2.4
Name: gmail-cleanup-tool
Version: 1.0.0
Summary: A GUI-based Gmail cleanup tool for safely managing email storage
Author-email: Gmail Cleanup Tool <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/example/gmail-cleanup-tool
Project-URL: Repository, https://github.com/example/gmail-cleanup-tool
Project-URL: Issues, https://github.com/example/gmail-cleanup-tool/issues
Keywords: gmail,email,cleanup,gui,desktop
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: End Users/Desktop
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Communications :: Email
Classifier: Topic :: Desktop Environment
Classifier: Topic :: Utilities
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: PySide6>=6.5.0
Requires-Dist: google-api-python-client>=2.100.0
Requires-Dist: google-auth-httplib2>=0.1.0
Requires-Dist: google-auth-oauthlib>=1.0.0
Requires-Dist: google-auth>=2.20.0
Requires-Dist: keyring>=24.0.0
Requires-Dist: cryptography>=41.0.0
Requires-Dist: pyyaml>=6.0
Requires-Dist: python-dateutil>=2.8.0
Requires-Dist: packaging>=23.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-qt>=4.2.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Requires-Dist: mypy>=1.5.0; extra == "dev"
Requires-Dist: pre-commit>=3.0.0; extra == "dev"
Provides-Extra: build
Requires-Dist: pyinstaller>=5.13.0; extra == "build"
Requires-Dist: auto-py-to-exe>=2.40.0; extra == "build"
Dynamic: license-file

# Gmail Cleanup Tool

A desktop GUI application for safely identifying and deleting unwanted Gmail messages based on configurable criteria. Built with Python and PySide6, this tool prioritizes preview-first safety, reversibility, and clear reporting.

## Features

### 🔒 Safety First
- **Preview-first approach**: All operations start with a safe preview
- **Reversible actions**: Default to Trash (30-day recovery window)
- **Protection rules**: Never delete important messages (invoices, receipts, etc.)
- **Confirmation dialogs**: Multiple confirmations for destructive actions
- **Backup system**: Export messages before deletion

### 🎯 Powerful Filtering
- **Multiple criteria**: Filter by sender, subject, age, size, labels, categories
- **Gmail categories**: Target Promotions, Social, Updates, Forums automatically
- **Advanced patterns**: Regex support for complex matching
- **Thread handling**: Process individual messages or entire threads
- **Allow/Block lists**: Manage trusted senders and domains

### 🖥️ User-Friendly GUI
- **Clean interface**: Intuitive sidebar navigation with dedicated tabs
- **Visual rule builder**: Drag-and-drop criteria configuration
- **Real-time preview**: See exactly what will be affected
- **Progress tracking**: Detailed progress bars and status updates
- **Comprehensive reporting**: Detailed logs and statistics

### 🔐 Secure Authentication
- **OAuth 2.0**: Secure Google authentication with minimal permissions
- **Token storage**: Encrypted local storage with OS keyring support
- **Scope management**: Request only necessary permissions
- **Easy setup**: Step-by-step authentication guide

## Quick Start

### Prerequisites
- Python 3.8 or higher
- Gmail account with API access enabled

### Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/example/gmail-cleanup-tool.git
   cd gmail-cleanup-tool
   ```

2. **Install dependencies**:
   ```bash
   pip install -e .
   ```

3. **Run the application**:
   ```bash
   gmail-cleanup
   ```

### First-Time Setup

1. **Configure OAuth credentials** (see [OAuth Setup Guide](#oauth-setup) below)
2. **Authenticate with Gmail** in the Settings tab
3. **Create your first cleanup profile** in the Criteria Builder
4. **Run a preview** to see what would be cleaned up
5. **Execute cleanup** when you're satisfied with the preview

## OAuth Setup Guide

To use this tool, you need to create OAuth credentials in the Google Cloud Console:

### Step 1: Create a Google Cloud Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Gmail API:
   - Go to "APIs & Services" > "Library"
   - Search for "Gmail API"
   - Click "Enable"

### Step 2: Create OAuth Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. Configure the OAuth consent screen if prompted:
   - Choose "External" user type
   - Fill in required fields (app name, user support email, etc.)
   - Add your email to test users
4. Create OAuth client ID:
   - Application type: "Desktop application"
   - Name: "Gmail Cleanup Tool"
5. Download the credentials JSON file

### Step 3: Configure the Application

1. Open the Gmail Cleanup Tool
2. Go to the Settings tab
3. Enter your Client ID and Client Secret from the downloaded JSON
4. Click "Authenticate" and follow the browser prompts
5. Grant the requested permissions

**Required Scopes:**
- `https://www.googleapis.com/auth/gmail.readonly` - Read email metadata
- `https://www.googleapis.com/auth/gmail.modify` - Move emails to trash/archive

## Usage Guide

### Creating Cleanup Rules

1. **Go to Criteria Builder tab**
2. **Add filter criteria**:
   - Sender patterns (domains, exact addresses, keywords)
   - Subject patterns (promotional keywords, spam indicators)
   - Email age (older than X days/months/years)
   - Email size (larger than X MB)
   - Gmail categories (Promotions, Social, Updates, etc.)
3. **Configure safety settings**:
   - Skip starred/important messages
   - Skip contacts
   - Add protected terms
4. **Save as a profile** for reuse

### Running Cleanup

1. **Preview first**: Always start with "Run Preview"
2. **Review candidates**: Check the Preview tab for affected messages
3. **Adjust if needed**: Exclude specific messages or modify rules
4. **Execute cleanup**: Use the Cleanup tab when satisfied
5. **Monitor progress**: Watch real-time progress and statistics

### Safety Features

- **Protected terms**: Automatically skip messages containing financial/legal terms
- **Starred/Important**: Skip messages marked as starred or important
- **Contacts**: Optionally skip messages from your contacts
- **Confirmation dialogs**: Multiple confirmations for bulk operations
- **Undo capability**: Restore from trash within 30 days

## Configuration

### Profiles

Cleanup profiles are stored as JSON/YAML files and can be imported/exported:

```yaml
name: "Monthly Cleanup"
description: "Remove old promotional emails"
rules:
  - field: "category"
    operator: "equals"
    value: "promotions"
  - field: "date"
    operator: "older_than"
    value: "90d"
safety_settings:
  skip_starred: true
  skip_important: true
  protected_terms: ["invoice", "receipt", "tax"]
action: "trash"
```

### Settings

Key configuration options:
- **Batch size**: Number of messages processed per batch (default: 100)
- **Delay**: Pause between batches to respect API limits (default: 1000ms)
- **Max messages**: Safety limit for single run (default: 10,000)

## Development

### Setting up Development Environment

```bash
# Clone repository
git clone https://github.com/example/gmail-cleanup-tool.git
cd gmail-cleanup-tool

# Install in development mode with dev dependencies
pip install -e ".[dev]"

# Install pre-commit hooks
pre-commit install

# Run tests
pytest

# Run with coverage
pytest --cov=gmail_cleanup
```

### Project Structure

```
src/gmail_cleanup/
├── main.py              # Application entry point
├── core/                # Core functionality
│   ├── config.py        # Configuration management
│   └── logging_config.py # Logging setup
├── auth/                # Authentication
│   ├── oauth_manager.py # OAuth flow management
│   └── token_storage.py # Secure token storage
├── data/                # Data models and database
│   ├── models.py        # Data structures
│   └── database.py      # SQLite operations
├── gui/                 # User interface
│   ├── main_window.py   # Main application window
│   └── tabs/            # Individual tab implementations
└── api/                 # Gmail API integration
    └── gmail_client.py  # Gmail API wrapper
```

## Building Executables

Create standalone executables for distribution:

```bash
# Install build dependencies
pip install -e ".[build]"

# Build for current platform
pyinstaller gmail_cleanup.spec

# Output will be in dist/ directory
```

## Troubleshooting

### Common Issues

**Authentication fails**:
- Verify OAuth credentials are correct
- Check that Gmail API is enabled in Google Cloud Console
- Ensure redirect URI is set to `http://localhost:8080`

**API quota exceeded**:
- Reduce batch size in settings
- Increase delay between batches
- Check Google Cloud Console for quota limits

**Permission denied errors**:
- Verify required OAuth scopes are granted
- Re-authenticate if scopes have changed

### Logs

Application logs are stored in:
- **Windows**: `%APPDATA%/GmailCleanupTool/logs/`
- **macOS**: `~/Library/Application Support/GmailCleanupTool/logs/`
- **Linux**: `~/.config/GmailCleanupTool/logs/`

## License

MIT License - see [LICENSE](LICENSE) file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## Support

- **Issues**: [GitHub Issues](https://github.com/example/gmail-cleanup-tool/issues)
- **Documentation**: [Wiki](https://github.com/example/gmail-cleanup-tool/wiki)
- **Discussions**: [GitHub Discussions](https://github.com/example/gmail-cleanup-tool/discussions)
