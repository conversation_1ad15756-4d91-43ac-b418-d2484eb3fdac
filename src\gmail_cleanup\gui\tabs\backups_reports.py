"""
Backups & Reports tab for the Gmail Cleanup Tool.

Manages backup files and cleanup reports with restore functionality.
"""

import logging
import os
import json
import zipfile
from typing import Optional, List, Dict, Any
from datetime import datetime
from pathlib import Path

from PySide6.QtWidgets import (
    Q<PERSON>abel, <PERSON>Widget, QVBoxLayout, QHBoxLayout, QGroupBox, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QTextEdit, QSplitter,
    QComboBox, QLineEdit, QProgressBar, QMessageBox, QFileDialog,
    QTabWidget, QFrame, QFormLayout, QCheckBox, QSpinBox
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont

from .base_tab import BaseTab
from ...core.config import AppConfig
from ...data.models import <PERSON>upRun, CleanupCandidate, RunStatus, CleanupAction
from ...data.database import DatabaseManager
from ...auth.oauth_manager import <PERSON>AuthManager
from ...api.gmail_client import GmailClient
from ...core.logging_config import log_user_action

logger = logging.getLogger(__name__)


class RestoreWorker(QThread):
    """Worker thread for restore operations."""

    progress_updated = Signal(str, int, int)  # message, current, total
    status_updated = Signal(str)
    restore_completed = Signal(int, int)  # success_count, error_count
    error_occurred = Signal(str)

    def __init__(self, gmail_client: GmailClient, message_ids: List[str]):
        super().__init__()
        self.gmail_client = gmail_client
        self.message_ids = message_ids

    def run(self):
        """Run restore operation in background thread."""
        try:
            success_count = 0
            error_count = 0

            for i, message_id in enumerate(self.message_ids):
                self.progress_updated.emit(f"Restoring message {i+1}", i+1, len(self.message_ids))

                try:
                    if self.gmail_client.untrash_message(message_id):
                        success_count += 1
                    else:
                        error_count += 1
                except Exception as e:
                    logger.error(f"Failed to restore message {message_id}: {e}")
                    error_count += 1

                # Small delay to respect rate limits
                self.msleep(100)

            self.restore_completed.emit(success_count, error_count)

        except Exception as e:
            logger.error(f"Restore worker error: {e}")
            self.error_occurred.emit(str(e))


class BackupsReportsTab(BaseTab):
    """Backups & Reports tab for managing backups and reports."""

    def __init__(self, config: AppConfig, parent: Optional[QWidget] = None):
        # Initialize attributes first
        self.database = DatabaseManager(config)
        self.oauth_manager = OAuthManager(config)
        self.gmail_client = None

        self.current_run: Optional[CleanupRun] = None
        self.restore_worker = None

        # Then call parent constructor
        super().__init__(config, parent)

    def _setup_ui(self) -> None:
        """Set up the backups & reports UI."""
        # Title
        title = QLabel("Backups & Reports")
        title.setFont(QFont("Arial", 20, QFont.Bold))
        title.setStyleSheet("color: #333; margin-bottom: 10px;")
        self.layout.addWidget(title)

        # Create tab widget for different sections
        tab_widget = QTabWidget()

        # Run History tab
        self._create_run_history_tab(tab_widget)

        # Backup Management tab
        self._create_backup_management_tab(tab_widget)

        # Restore Operations tab
        self._create_restore_operations_tab(tab_widget)

        self.layout.addWidget(tab_widget)

    def _create_run_history_tab(self, parent) -> None:
        """Create the run history tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Controls
        controls_layout = QHBoxLayout()

        controls_layout.addWidget(QLabel("Filter:"))
        self.filter_combo = QComboBox()
        self.filter_combo.addItems([
            "All runs", "Preview only", "Cleanup only", "Completed", "Failed"
        ])
        self.filter_combo.currentTextChanged.connect(self._refresh_run_history)
        controls_layout.addWidget(self.filter_combo)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search by profile name...")
        self.search_edit.textChanged.connect(self._refresh_run_history)
        controls_layout.addWidget(self.search_edit)

        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(self._refresh_run_history)
        controls_layout.addWidget(refresh_btn)

        controls_layout.addStretch()
        layout.addLayout(controls_layout)

        # Splitter for table and details
        splitter = QSplitter(Qt.Vertical)

        # Run history table
        self.runs_table = QTableWidget()
        self.runs_table.setColumnCount(8)
        self.runs_table.setHorizontalHeaderLabels([
            "Date", "Profile", "Action", "Status", "Candidates", "Success", "Errors", "Space (MB)"
        ])

        # Configure table
        header = self.runs_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Date
        header.setSectionResizeMode(1, QHeaderView.Stretch)           # Profile
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Action
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Status
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Candidates
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Success
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Errors
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # Space

        self.runs_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.runs_table.setAlternatingRowColors(True)
        self.runs_table.setSortingEnabled(True)
        self.runs_table.itemSelectionChanged.connect(self._on_run_selected)

        splitter.addWidget(self.runs_table)

        # Run details
        details_group = QGroupBox("Run Details")
        details_layout = QVBoxLayout(details_group)

        self.run_details_text = QTextEdit()
        self.run_details_text.setReadOnly(True)
        self.run_details_text.setMaximumHeight(200)
        details_layout.addWidget(self.run_details_text)

        # Action buttons
        details_buttons = QHBoxLayout()

        self.view_candidates_btn = QPushButton("👁️ View Candidates")
        self.view_candidates_btn.clicked.connect(self._view_candidates)
        self.view_candidates_btn.setEnabled(False)
        details_buttons.addWidget(self.view_candidates_btn)

        self.restore_run_btn = QPushButton("↶ Restore from Trash")
        self.restore_run_btn.clicked.connect(self._restore_from_run)
        self.restore_run_btn.setEnabled(False)
        details_buttons.addWidget(self.restore_run_btn)

        self.export_run_btn = QPushButton("💾 Export Report")
        self.export_run_btn.clicked.connect(self._export_run_report)
        self.export_run_btn.setEnabled(False)
        details_buttons.addWidget(self.export_run_btn)

        self.delete_run_btn = QPushButton("🗑️ Delete Run")
        self.delete_run_btn.clicked.connect(self._delete_run)
        self.delete_run_btn.setEnabled(False)
        details_buttons.addWidget(self.delete_run_btn)

        details_buttons.addStretch()
        details_layout.addLayout(details_buttons)

        splitter.addWidget(details_group)
        splitter.setSizes([400, 200])

        layout.addWidget(splitter)

        parent.addTab(tab, "📊 Run History")

        # Load initial data
        self._refresh_run_history()

    def _create_backup_management_tab(self, parent) -> None:
        """Create the backup management tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Backup directory info
        info_group = QGroupBox("Backup Information")
        info_layout = QFormLayout(info_group)

        backup_dir = self.config.get_backups_dir()
        info_layout.addRow("Backup Directory:", QLabel(str(backup_dir)))

        # Calculate backup size
        total_size = 0
        file_count = 0
        if backup_dir.exists():
            for file_path in backup_dir.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
                    file_count += 1

        size_mb = total_size / (1024 * 1024)
        info_layout.addRow("Total Size:", QLabel(f"{size_mb:.1f} MB"))
        info_layout.addRow("File Count:", QLabel(f"{file_count:,} files"))

        layout.addWidget(info_group)

        # Backup settings
        settings_group = QGroupBox("Backup Settings")
        settings_layout = QFormLayout(settings_group)

        self.auto_backup_cb = QCheckBox("Automatically backup before cleanup")
        self.auto_backup_cb.setChecked(True)
        settings_layout.addRow("Auto Backup:", self.auto_backup_cb)

        self.backup_format_combo = QComboBox()
        self.backup_format_combo.addItems(["JSON", "CSV", "EML (Raw Email)"])
        settings_layout.addRow("Backup Format:", self.backup_format_combo)

        self.max_backup_age_spin = QSpinBox()
        self.max_backup_age_spin.setRange(1, 365)
        self.max_backup_age_spin.setValue(90)
        self.max_backup_age_spin.setSuffix(" days")
        settings_layout.addRow("Keep backups for:", self.max_backup_age_spin)

        layout.addWidget(settings_group)

        # Backup actions
        actions_group = QGroupBox("Backup Actions")
        actions_layout = QVBoxLayout(actions_group)

        actions_buttons = QHBoxLayout()

        create_backup_btn = QPushButton("📦 Create Manual Backup")
        create_backup_btn.clicked.connect(self._create_manual_backup)
        actions_buttons.addWidget(create_backup_btn)

        cleanup_backups_btn = QPushButton("🧹 Cleanup Old Backups")
        cleanup_backups_btn.clicked.connect(self._cleanup_old_backups)
        actions_buttons.addWidget(cleanup_backups_btn)

        open_backup_dir_btn = QPushButton("📁 Open Backup Directory")
        open_backup_dir_btn.clicked.connect(self._open_backup_directory)
        actions_buttons.addWidget(open_backup_dir_btn)

        actions_buttons.addStretch()
        actions_layout.addLayout(actions_buttons)

        layout.addWidget(actions_group)

        layout.addStretch()

        parent.addTab(tab, "💾 Backup Management")

    def _create_restore_operations_tab(self, parent) -> None:
        """Create the restore operations tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Instructions
        instructions = QLabel("""
Restore Operations allow you to recover messages that were moved to trash during cleanup operations.
Note: Messages can only be restored within Gmail's trash retention period (~30 days).
        """)
        instructions.setWordWrap(True)
        instructions.setStyleSheet("QLabel { background-color: #e3f2fd; padding: 10px; border-radius: 5px; }")
        layout.addWidget(instructions)

        # Restore from run
        restore_group = QGroupBox("Restore from Cleanup Run")
        restore_layout = QVBoxLayout(restore_group)

        run_selection_layout = QHBoxLayout()
        run_selection_layout.addWidget(QLabel("Select Run:"))

        self.restore_run_combo = QComboBox()
        self.restore_run_combo.currentTextChanged.connect(self._on_restore_run_selected)
        run_selection_layout.addWidget(self.restore_run_combo)

        refresh_runs_btn = QPushButton("🔄 Refresh")
        refresh_runs_btn.clicked.connect(self._refresh_restore_runs)
        run_selection_layout.addWidget(refresh_runs_btn)

        run_selection_layout.addStretch()
        restore_layout.addLayout(run_selection_layout)

        # Restore info
        self.restore_info_text = QTextEdit()
        self.restore_info_text.setMaximumHeight(100)
        self.restore_info_text.setReadOnly(True)
        restore_layout.addWidget(self.restore_info_text)

        # Restore controls
        restore_controls = QHBoxLayout()

        self.restore_btn = QPushButton("↶ Restore All Messages")
        self.restore_btn.clicked.connect(self._start_restore)
        self.restore_btn.setEnabled(False)
        restore_controls.addWidget(self.restore_btn)

        self.cancel_restore_btn = QPushButton("❌ Cancel")
        self.cancel_restore_btn.clicked.connect(self._cancel_restore)
        self.cancel_restore_btn.setEnabled(False)
        restore_controls.addWidget(self.cancel_restore_btn)

        restore_controls.addStretch()
        restore_layout.addLayout(restore_controls)

        # Progress
        self.restore_progress = QProgressBar()
        self.restore_progress.setVisible(False)
        restore_layout.addWidget(self.restore_progress)

        self.restore_status = QLabel("Select a cleanup run to restore")
        restore_layout.addWidget(self.restore_status)

        layout.addWidget(restore_group)

        # Manual restore
        manual_group = QGroupBox("Manual Restore")
        manual_layout = QFormLayout(manual_group)

        self.message_ids_edit = QTextEdit()
        self.message_ids_edit.setMaximumHeight(80)
        self.message_ids_edit.setPlaceholderText("Enter Gmail message IDs, one per line...")
        manual_layout.addRow("Message IDs:", self.message_ids_edit)

        manual_restore_btn = QPushButton("↶ Restore Messages")
        manual_restore_btn.clicked.connect(self._manual_restore)
        manual_layout.addRow("", manual_restore_btn)

        layout.addWidget(manual_group)

        layout.addStretch()

        parent.addTab(tab, "↶ Restore Operations")

        # Load restore runs
        self._refresh_restore_runs()

    def _refresh_run_history(self) -> None:
        """Refresh the run history table."""
        filter_text = self.filter_combo.currentText()
        search_text = self.search_edit.text().lower()

        # Get runs from database
        runs = self.database.list_runs(limit=100)

        # Apply filters
        filtered_runs = []
        for run in runs:
            # Filter by type
            if filter_text == "Preview only" and run.status != RunStatus.COMPLETED:
                continue
            elif filter_text == "Cleanup only" and run.action == CleanupAction.TRASH:
                continue
            elif filter_text == "Completed" and run.status != RunStatus.COMPLETED:
                continue
            elif filter_text == "Failed" and run.status != RunStatus.FAILED:
                continue

            # Filter by search text
            if search_text and search_text not in run.profile_name.lower():
                continue

            filtered_runs.append(run)

        # Populate table
        self.runs_table.setRowCount(len(filtered_runs))

        for row, run in enumerate(filtered_runs):
            # Date
            date_item = QTableWidgetItem(run.started_at.strftime("%Y-%m-%d %H:%M"))
            self.runs_table.setItem(row, 0, date_item)

            # Profile
            profile_item = QTableWidgetItem(run.profile_name)
            self.runs_table.setItem(row, 1, profile_item)

            # Action
            action_item = QTableWidgetItem(run.action.value.replace('_', ' ').title())
            self.runs_table.setItem(row, 2, action_item)

            # Status
            status_item = QTableWidgetItem(run.status.value.replace('_', ' ').title())
            if run.status == RunStatus.COMPLETED:
                status_item.setBackground(Qt.lightGreen)
            elif run.status == RunStatus.FAILED:
                status_item.setBackground(Qt.lightCoral)
            elif run.status == RunStatus.CANCELLED:
                status_item.setBackground(Qt.yellow)
            self.runs_table.setItem(row, 3, status_item)

            # Candidates
            candidates_item = QTableWidgetItem(f"{run.total_candidates:,}")
            self.runs_table.setItem(row, 4, candidates_item)

            # Success
            success_item = QTableWidgetItem(f"{run.success_count:,}")
            self.runs_table.setItem(row, 5, success_item)

            # Errors
            errors_item = QTableWidgetItem(f"{run.error_count:,}")
            self.runs_table.setItem(row, 6, errors_item)

            # Space
            space_item = QTableWidgetItem(f"{run.actual_space_mb:.1f}")
            self.runs_table.setItem(row, 7, space_item)

            # Store run data
            date_item.setData(Qt.UserRole, run)

        # Sort by date (newest first)
        self.runs_table.sortItems(0, Qt.DescendingOrder)

    def _on_run_selected(self) -> None:
        """Handle run selection in the table."""
        current_row = self.runs_table.currentRow()
        if current_row < 0:
            self.current_run = None
            self._clear_run_details()
            return

        # Get run data
        date_item = self.runs_table.item(current_row, 0)
        if date_item:
            self.current_run = date_item.data(Qt.UserRole)
            self._display_run_details(self.current_run)

    def _display_run_details(self, run: CleanupRun) -> None:
        """Display detailed information about a run."""
        details_text = f"""
Run ID: {run.run_id}
Profile: {run.profile_name}
Action: {run.action.value.replace('_', ' ').title()}
Status: {run.status.value.replace('_', ' ').title()}

Started: {run.started_at.strftime('%Y-%m-%d %H:%M:%S')}
Completed: {run.completed_at.strftime('%Y-%m-%d %H:%M:%S') if run.completed_at else 'N/A'}
Duration: {(run.completed_at - run.started_at).total_seconds():.1f if run.completed_at else 0} seconds

Results:
- Total candidates: {run.total_candidates:,}
- Successfully processed: {run.success_count:,}
- Errors: {run.error_count:,}
- Estimated space: {run.estimated_space_mb:.1f} MB
- Actual space: {run.actual_space_mb:.1f} MB
"""

        if run.error_messages:
            details_text += f"\nRecent errors:\n"
            for error in run.error_messages[:3]:
                details_text += f"• {error}\n"

        self.run_details_text.setPlainText(details_text)

        # Enable/disable buttons based on run type
        self.view_candidates_btn.setEnabled(True)
        self.export_run_btn.setEnabled(True)
        self.delete_run_btn.setEnabled(True)

        # Enable restore only for trash operations
        self.restore_run_btn.setEnabled(
            run.action == CleanupAction.TRASH and
            run.status == RunStatus.COMPLETED and
            run.success_count > 0
        )

    def _clear_run_details(self) -> None:
        """Clear run details display."""
        self.run_details_text.clear()
        self.view_candidates_btn.setEnabled(False)
        self.restore_run_btn.setEnabled(False)
        self.export_run_btn.setEnabled(False)
        self.delete_run_btn.setEnabled(False)

    def _view_candidates(self) -> None:
        """View candidates for the selected run."""
        if not self.current_run:
            return

        candidates = self.database.get_run_candidates(self.current_run.run_id)

        # Create a simple dialog to show candidates
        dialog = QMessageBox(self)
        dialog.setWindowTitle(f"Candidates for Run {self.current_run.run_id[:8]}")
        dialog.setIcon(QMessageBox.Information)

        if candidates:
            text = f"Found {len(candidates)} candidates:\n\n"
            for i, candidate in enumerate(candidates[:10]):  # Show first 10
                status = "Excluded" if candidate.excluded else "Active"
                text += f"{i+1}. {candidate.message.subject[:50]}... ({status})\n"

            if len(candidates) > 10:
                text += f"\n... and {len(candidates) - 10} more candidates"
        else:
            text = "No candidates found for this run."

        dialog.setText(text)
        dialog.exec()

    def _restore_from_run(self) -> None:
        """Restore messages from the selected run."""
        if not self.current_run:
            return

        if not self.require_authentication():
            return

        reply = QMessageBox.question(
            self,
            "Restore Messages",
            f"This will restore {self.current_run.success_count:,} messages from Gmail Trash.\n\n"
            "Are you sure you want to continue?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Get candidates and extract message IDs
            candidates = self.database.get_run_candidates(self.current_run.run_id)
            message_ids = [c.message.message_id for c in candidates if not c.excluded]

            self._start_restore_operation(message_ids)

    def _export_run_report(self) -> None:
        """Export detailed report for the selected run."""
        if not self.current_run:
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Export Run Report",
            f"run_report_{self.current_run.run_id[:8]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON Files (*.json);;Text Files (*.txt);;All Files (*)"
        )

        if file_path:
            try:
                # Get candidates
                candidates = self.database.get_run_candidates(self.current_run.run_id)

                # Create comprehensive report
                report = {
                    "run": self.current_run.to_dict(),
                    "candidates": [c.to_dict() for c in candidates],
                    "export_timestamp": datetime.now().isoformat(),
                    "export_version": "1.0"
                }

                if file_path.endswith('.json'):
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(report, f, indent=2, ensure_ascii=False)
                else:
                    # Export as text
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(f"Gmail Cleanup Tool - Run Report\n")
                        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                        f.write(f"Run Details:\n")
                        f.write(f"- ID: {self.current_run.run_id}\n")
                        f.write(f"- Profile: {self.current_run.profile_name}\n")
                        f.write(f"- Action: {self.current_run.action.value}\n")
                        f.write(f"- Status: {self.current_run.status.value}\n")
                        f.write(f"- Started: {self.current_run.started_at}\n")
                        f.write(f"- Completed: {self.current_run.completed_at}\n")
                        f.write(f"- Success: {self.current_run.success_count}\n")
                        f.write(f"- Errors: {self.current_run.error_count}\n\n")

                        f.write(f"Candidates ({len(candidates)}):\n")
                        for i, candidate in enumerate(candidates, 1):
                            status = "Excluded" if candidate.excluded else "Active"
                            f.write(f"{i}. {candidate.message.subject} ({status})\n")
                            f.write(f"   From: {candidate.message.sender}\n")
                            f.write(f"   Date: {candidate.message.date}\n")
                            f.write(f"   Reasons: {'; '.join(candidate.reasons)}\n\n")

                self.emit_info("Export Successful", f"Report exported to {file_path}")

            except Exception as e:
                self.emit_error("Export Failed", f"Failed to export report: {e}")

    def _delete_run(self) -> None:
        """Delete the selected run from database."""
        if not self.current_run:
            return

        reply = QMessageBox.question(
            self,
            "Delete Run",
            f"Are you sure you want to delete this run?\n\n"
            f"Run ID: {self.current_run.run_id}\n"
            f"Profile: {self.current_run.profile_name}\n"
            f"Date: {self.current_run.started_at.strftime('%Y-%m-%d %H:%M')}\n\n"
            "This action cannot be undone.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # TODO: Implement run deletion in database
                self.emit_info("Delete Run", "Run deletion will be implemented in database manager.")
                self._refresh_run_history()

            except Exception as e:
                self.emit_error("Delete Failed", f"Failed to delete run: {e}")

    def _refresh_restore_runs(self) -> None:
        """Refresh the list of runs available for restore."""
        self.restore_run_combo.clear()
        self.restore_run_combo.addItem("-- Select Run --")

        # Get trash operations that completed successfully
        runs = self.database.list_runs(limit=50)
        restore_runs = [
            r for r in runs
            if r.action == CleanupAction.TRASH and
               r.status == RunStatus.COMPLETED and
               r.success_count > 0
        ]

        for run in restore_runs:
            display_text = f"{run.started_at.strftime('%Y-%m-%d %H:%M')} - {run.profile_name} ({run.success_count:,} messages)"
            self.restore_run_combo.addItem(display_text)
            self.restore_run_combo.setItemData(self.restore_run_combo.count() - 1, run)

    def _on_restore_run_selected(self, text: str) -> None:
        """Handle restore run selection."""
        if text == "-- Select Run --":
            self.restore_info_text.clear()
            self.restore_btn.setEnabled(False)
            return

        # Get selected run
        index = self.restore_run_combo.currentIndex()
        if index > 0:
            run = self.restore_run_combo.itemData(index)
            if run:
                info_text = f"""
Selected Run Information:
- Profile: {run.profile_name}
- Date: {run.started_at.strftime('%Y-%m-%d %H:%M:%S')}
- Messages processed: {run.success_count:,}
- Space reclaimed: {run.actual_space_mb:.1f} MB

Note: This will attempt to restore all successfully processed messages from Gmail Trash.
Messages may not be restorable if they have been permanently deleted or if the trash retention period has expired.
                """
                self.restore_info_text.setPlainText(info_text.strip())
                self.restore_btn.setEnabled(True)

    def _start_restore(self) -> None:
        """Start restore operation for selected run."""
        if not self.require_authentication():
            return

        index = self.restore_run_combo.currentIndex()
        if index <= 0:
            return

        run = self.restore_run_combo.itemData(index)
        if not run:
            return

        # Get message IDs to restore
        candidates = self.database.get_run_candidates(run.run_id)
        message_ids = [c.message.message_id for c in candidates if not c.excluded]

        self._start_restore_operation(message_ids)

    def _start_restore_operation(self, message_ids: List[str]) -> None:
        """Start the actual restore operation."""
        if not message_ids:
            self.emit_warning("No Messages", "No messages to restore.")
            return

        try:
            # Initialize Gmail client
            if not self.gmail_client:
                self.gmail_client = GmailClient(self.oauth_manager)

            # Start restore worker
            self.restore_worker = RestoreWorker(self.gmail_client, message_ids)
            self.restore_worker.progress_updated.connect(self._on_restore_progress)
            self.restore_worker.status_updated.connect(self._on_restore_status)
            self.restore_worker.restore_completed.connect(self._on_restore_completed)
            self.restore_worker.error_occurred.connect(self._on_restore_error)

            # Update UI
            self.restore_btn.setEnabled(False)
            self.cancel_restore_btn.setEnabled(True)
            self.restore_progress.setVisible(True)
            self.restore_progress.setRange(0, len(message_ids))
            self.restore_progress.setValue(0)

            self.restore_worker.start()

            log_user_action("restore_started", {"message_count": len(message_ids)})

        except Exception as e:
            self.emit_error("Restore Error", f"Failed to start restore: {e}")

    def _cancel_restore(self) -> None:
        """Cancel the restore operation."""
        if self.restore_worker and self.restore_worker.isRunning():
            self.restore_worker.terminate()
            self.restore_worker.wait(3000)

        self._reset_restore_ui()
        self.restore_status.setText("Restore operation cancelled")

    def _on_restore_progress(self, message: str, current: int, total: int) -> None:
        """Handle restore progress updates."""
        self.restore_progress.setValue(current)
        self.restore_status.setText(f"{message} ({current}/{total})")

    def _on_restore_status(self, status: str) -> None:
        """Handle restore status updates."""
        self.restore_status.setText(status)

    def _on_restore_completed(self, success_count: int, error_count: int) -> None:
        """Handle restore completion."""
        self._reset_restore_ui()

        message = f"Restore completed:\n• Successfully restored: {success_count:,} messages\n• Errors: {error_count:,} messages"

        if error_count > 0:
            message += "\n\nSome messages could not be restored. They may have been permanently deleted or the trash retention period may have expired."

        self.emit_info("Restore Completed", message)

        log_user_action("restore_completed", {
            "success_count": success_count,
            "error_count": error_count
        })

    def _on_restore_error(self, error: str) -> None:
        """Handle restore errors."""
        self._reset_restore_ui()
        self.emit_error("Restore Failed", error)

    def _reset_restore_ui(self) -> None:
        """Reset restore UI to ready state."""
        self.restore_btn.setEnabled(True)
        self.cancel_restore_btn.setEnabled(False)
        self.restore_progress.setVisible(False)

    def _manual_restore(self) -> None:
        """Perform manual restore of specified message IDs."""
        if not self.require_authentication():
            return

        message_ids_text = self.message_ids_edit.toPlainText().strip()
        if not message_ids_text:
            self.emit_warning("No Message IDs", "Please enter Gmail message IDs to restore.")
            return

        # Parse message IDs
        message_ids = [mid.strip() for mid in message_ids_text.split('\n') if mid.strip()]

        if not message_ids:
            self.emit_warning("Invalid Input", "No valid message IDs found.")
            return

        reply = QMessageBox.question(
            self,
            "Manual Restore",
            f"This will attempt to restore {len(message_ids)} messages from Gmail Trash.\n\n"
            "Are you sure you want to continue?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self._start_restore_operation(message_ids)

    # Backup management methods
    def _create_manual_backup(self) -> None:
        """Create a manual backup."""
        self.emit_info("Manual Backup", "Manual backup creation will be implemented in a future version.")

    def _cleanup_old_backups(self) -> None:
        """Clean up old backup files."""
        max_age_days = self.max_backup_age_spin.value()
        backup_dir = self.config.get_backups_dir()

        if not backup_dir.exists():
            self.emit_info("No Backups", "No backup directory found.")
            return

        # TODO: Implement backup cleanup
        self.emit_info("Cleanup Backups", f"Backup cleanup (older than {max_age_days} days) will be implemented.")

    def _open_backup_directory(self) -> None:
        """Open the backup directory in file explorer."""
        backup_dir = self.config.get_backups_dir()
        backup_dir.mkdir(parents=True, exist_ok=True)

        import subprocess
        import platform

        try:
            if platform.system() == "Windows":
                subprocess.run(["explorer", str(backup_dir)])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", str(backup_dir)])
            else:  # Linux
                subprocess.run(["xdg-open", str(backup_dir)])
        except Exception as e:
            self.emit_error("Open Directory", f"Failed to open backup directory: {e}")

    def show_run_details(self, run: CleanupRun) -> None:
        """Show details for a specific run (called from other tabs)."""
        self.current_run = run

        # Switch to run history tab and select the run
        # This would require finding the run in the table and selecting it
        self._refresh_run_history()

        # Find and select the run in the table
        for row in range(self.runs_table.rowCount()):
            date_item = self.runs_table.item(row, 0)
            if date_item and date_item.data(Qt.UserRole) == run:
                self.runs_table.selectRow(row)
                break

    def on_tab_activated(self) -> None:
        """Called when tab becomes active."""
        super().on_tab_activated()
        self._refresh_run_history()
        self._refresh_restore_runs()
