"""
Dashboard tab for the Gmail Cleanup Tool.

Provides overview of account status, recent activity, and quick actions.
"""

import logging
from typing import Optional, Dict, Any

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QGridLayout, QGroupBox, QProgressBar, QTextEdit
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont, QPixmap

from .base_tab import BaseTab
from ...core.config import AppConfig

logger = logging.getLogger(__name__)


class StatCard(QFrame):
    """A card widget for displaying statistics."""
    
    def __init__(self, title: str, value: str = "0", subtitle: str = "", parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 8px;
                background-color: white;
                padding: 10px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Title
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 10))
        title_label.setStyleSheet("color: #666; font-weight: bold;")
        layout.addWidget(title_label)
        
        # Value
        self.value_label = QLabel(value)
        self.value_label.setFont(QFont("Arial", 24, QFont.Bold))
        self.value_label.setStyleSheet("color: #333; margin: 5px 0;")
        layout.addWidget(self.value_label)
        
        # Subtitle
        if subtitle:
            subtitle_label = QLabel(subtitle)
            subtitle_label.setFont(QFont("Arial", 9))
            subtitle_label.setStyleSheet("color: #888;")
            layout.addWidget(subtitle_label)
    
    def update_value(self, value: str, subtitle: str = "") -> None:
        """Update the card value and subtitle."""
        self.value_label.setText(value)
        if subtitle and self.layout().count() > 2:
            subtitle_widget = self.layout().itemAt(2).widget()
            if isinstance(subtitle_widget, QLabel):
                subtitle_widget.setText(subtitle)


class DashboardTab(BaseTab):
    """Dashboard tab showing account overview and quick actions."""
    
    # Signals
    run_preview_requested = Signal()
    run_cleanup_requested = Signal()
    
    def __init__(self, config: AppConfig, parent: Optional[QWidget] = None):
        super().__init__(config, parent)
        
        # Data
        self.account_info: Optional[Dict[str, Any]] = None
        self.last_run_summary: Optional[Dict[str, Any]] = None
        
        # Start periodic refresh
        self.start_auto_refresh(30000)  # Refresh every 30 seconds
    
    def _setup_ui(self) -> None:
        """Set up the dashboard UI."""
        # Title
        title = QLabel("Dashboard")
        title.setFont(QFont("Arial", 20, QFont.Bold))
        title.setStyleSheet("color: #333; margin-bottom: 10px;")
        self.layout.addWidget(title)
        
        # Account status section
        self._create_account_section()
        
        # Statistics section
        self._create_stats_section()
        
        # Quick actions section
        self._create_actions_section()
        
        # Recent activity section
        self._create_activity_section()
        
        self.layout.addStretch()
    
    def _create_account_section(self) -> None:
        """Create the account status section."""
        group = QGroupBox("Account Status")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QHBoxLayout(group)
        
        # Account info
        self.account_label = QLabel("Not connected")
        self.account_label.setFont(QFont("Arial", 11))
        layout.addWidget(self.account_label)
        
        layout.addStretch()
        
        # Connection status indicator
        self.status_indicator = QLabel("●")
        self.status_indicator.setFont(QFont("Arial", 16))
        self.status_indicator.setStyleSheet("color: red;")
        layout.addWidget(self.status_indicator)
        
        self.layout.addWidget(group)
    
    def _create_stats_section(self) -> None:
        """Create the statistics section."""
        group = QGroupBox("Overview")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QGridLayout(group)
        
        # Create stat cards
        self.total_messages_card = StatCard("Total Messages", "0", "in your Gmail")
        self.candidates_card = StatCard("Cleanup Candidates", "0", "messages found")
        self.space_card = StatCard("Reclaimable Space", "0 MB", "estimated")
        self.last_run_card = StatCard("Last Run", "Never", "")
        
        # Add cards to grid
        layout.addWidget(self.total_messages_card, 0, 0)
        layout.addWidget(self.candidates_card, 0, 1)
        layout.addWidget(self.space_card, 1, 0)
        layout.addWidget(self.last_run_card, 1, 1)
        
        self.layout.addWidget(group)
    
    def _create_actions_section(self) -> None:
        """Create the quick actions section."""
        group = QGroupBox("Quick Actions")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QHBoxLayout(group)
        
        # Run Preview button
        self.preview_btn = QPushButton("🔍 Run Preview")
        self.preview_btn.setFont(QFont("Arial", 11))
        self.preview_btn.setMinimumHeight(40)
        self.preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #ccc;
                color: #666;
            }
        """)
        self.preview_btn.clicked.connect(self._on_run_preview)
        layout.addWidget(self.preview_btn)
        
        # Run Cleanup button
        self.cleanup_btn = QPushButton("🧹 Run Cleanup")
        self.cleanup_btn.setFont(QFont("Arial", 11))
        self.cleanup_btn.setMinimumHeight(40)
        self.cleanup_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #1e7e34;
            }
            QPushButton:disabled {
                background-color: #ccc;
                color: #666;
            }
        """)
        self.cleanup_btn.clicked.connect(self._on_run_cleanup)
        layout.addWidget(self.cleanup_btn)
        
        layout.addStretch()
        
        # Open Logs button
        self.logs_btn = QPushButton("📋 Open Logs")
        self.logs_btn.setFont(QFont("Arial", 11))
        self.logs_btn.setMinimumHeight(40)
        self.logs_btn.clicked.connect(self._on_open_logs)
        layout.addWidget(self.logs_btn)
        
        # Open Backups button
        self.backups_btn = QPushButton("💾 Open Backups")
        self.backups_btn.setFont(QFont("Arial", 11))
        self.backups_btn.setMinimumHeight(40)
        self.backups_btn.clicked.connect(self._on_open_backups)
        layout.addWidget(self.backups_btn)
        
        self.layout.addWidget(group)
    
    def _create_activity_section(self) -> None:
        """Create the recent activity section."""
        group = QGroupBox("Recent Activity")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QVBoxLayout(group)
        
        self.activity_text = QTextEdit()
        self.activity_text.setMaximumHeight(150)
        self.activity_text.setReadOnly(True)
        self.activity_text.setPlainText("No recent activity.")
        layout.addWidget(self.activity_text)
        
        self.layout.addWidget(group)
    
    def _setup_connections(self) -> None:
        """Set up signal connections."""
        super()._setup_connections()
        
        # Connect to main window tab changes to refresh when activated
        if self.main_window:
            self.main_window.tab_changed.connect(self._on_tab_changed)
    
    def _on_tab_changed(self, tab_id: str) -> None:
        """Handle tab change events."""
        if tab_id == "dashboard":
            self.on_tab_activated()
    
    def _on_run_preview(self) -> None:
        """Handle run preview button click."""
        if not self.require_authentication():
            return
        
        self.run_preview_requested.emit()
        if self.main_window:
            self.main_window.switch_to_tab("preview")
    
    def _on_run_cleanup(self) -> None:
        """Handle run cleanup button click."""
        if not self.require_authentication():
            return
        
        self.run_cleanup_requested.emit()
        if self.main_window:
            self.main_window.switch_to_tab("cleanup")
    
    def _on_open_logs(self) -> None:
        """Handle open logs button click."""
        if self.main_window:
            self.main_window.switch_to_tab("logs")
    
    def _on_open_backups(self) -> None:
        """Handle open backups button click."""
        if self.main_window:
            self.main_window.switch_to_tab("backups")
    
    def refresh_data(self) -> None:
        """Refresh dashboard data."""
        super().refresh_data()
        
        # Update account status
        self._update_account_status()
        
        # Update statistics
        self._update_statistics()
        
        # Update recent activity
        self._update_recent_activity()
        
        # Update button states
        self._update_button_states()
    
    def _update_account_status(self) -> None:
        """Update account connection status."""
        # TODO: Check actual authentication status
        is_connected = self.is_authenticated()
        
        if is_connected:
            self.account_label.setText("Connected to: <EMAIL>")  # TODO: Get actual email
            self.status_indicator.setStyleSheet("color: green;")
        else:
            self.account_label.setText("Not connected - Configure in Settings")
            self.status_indicator.setStyleSheet("color: red;")
    
    def _update_statistics(self) -> None:
        """Update statistics cards."""
        # TODO: Get actual statistics from data layer
        
        # Placeholder data
        self.total_messages_card.update_value("1,234", "messages in Gmail")
        self.candidates_card.update_value("567", "ready for cleanup")
        self.space_card.update_value("2.3 GB", "can be reclaimed")
        self.last_run_card.update_value("2 days ago", "Preview run")
    
    def _update_recent_activity(self) -> None:
        """Update recent activity log."""
        # TODO: Get actual recent activity from logs
        
        activity_text = """Recent Activity:
• 2024-01-15 14:30 - Preview run completed (567 candidates found)
• 2024-01-14 09:15 - Cleanup completed (234 messages moved to trash)
• 2024-01-13 16:45 - Profile "Monthly Cleanup" created
• 2024-01-12 11:20 - Authentication configured"""
        
        self.activity_text.setPlainText(activity_text)
    
    def _update_button_states(self) -> None:
        """Update button enabled/disabled states."""
        is_connected = self.is_authenticated()
        
        self.preview_btn.setEnabled(is_connected)
        self.cleanup_btn.setEnabled(is_connected)
        
        # Always enable logs and backups buttons
        self.logs_btn.setEnabled(True)
        self.backups_btn.setEnabled(True)
