"""
Database management for the Gmail Cleanup Tool.

Handles SQLite database operations for storing run history, profiles, and cached data.
"""

import sqlite3
import json
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid

from .models import CleanupProfile, CleanupRun, CleanupCandidate, EmailMessage, RunStatus
from ..core.config import AppConfig

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Manages SQLite database operations."""
    
    def __init__(self, config: AppConfig):
        self.config = config
        self.db_path = config.get_database_path()
        self._ensure_database()
    
    def _ensure_database(self) -> None:
        """Ensure database exists and has correct schema."""
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("PRAGMA foreign_keys = ON")
            self._create_tables(conn)
            logger.info(f"Database initialized at {self.db_path}")
    
    def _create_tables(self, conn: sqlite3.Connection) -> None:
        """Create database tables."""
        
        # Profiles table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS profiles (
                name TEXT PRIMARY KEY,
                description TEXT,
                data TEXT NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
        """)
        
        # Cleanup runs table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS cleanup_runs (
                run_id TEXT PRIMARY KEY,
                profile_name TEXT,
                status TEXT NOT NULL,
                action TEXT NOT NULL,
                started_at TEXT NOT NULL,
                completed_at TEXT,
                total_candidates INTEGER DEFAULT 0,
                processed_count INTEGER DEFAULT 0,
                success_count INTEGER DEFAULT 0,
                error_count INTEGER DEFAULT 0,
                estimated_space_mb REAL DEFAULT 0.0,
                actual_space_mb REAL DEFAULT 0.0,
                error_messages TEXT,
                FOREIGN KEY (profile_name) REFERENCES profiles (name)
            )
        """)
        
        # Run candidates table (for detailed tracking)
        conn.execute("""
            CREATE TABLE IF NOT EXISTS run_candidates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                run_id TEXT NOT NULL,
                message_id TEXT NOT NULL,
                thread_id TEXT NOT NULL,
                subject TEXT,
                sender TEXT,
                date TEXT,
                size_bytes INTEGER,
                labels TEXT,
                category TEXT,
                matched_rules TEXT,
                reasons TEXT,
                action TEXT,
                excluded INTEGER DEFAULT 0,
                processed INTEGER DEFAULT 0,
                success INTEGER DEFAULT 0,
                error_message TEXT,
                FOREIGN KEY (run_id) REFERENCES cleanup_runs (run_id)
            )
        """)
        
        # Message cache table (for performance)
        conn.execute("""
            CREATE TABLE IF NOT EXISTS message_cache (
                message_id TEXT PRIMARY KEY,
                thread_id TEXT,
                subject TEXT,
                sender TEXT,
                date TEXT,
                size_bytes INTEGER,
                labels TEXT,
                category TEXT,
                snippet TEXT,
                headers TEXT,
                is_starred INTEGER DEFAULT 0,
                is_important INTEGER DEFAULT 0,
                is_unread INTEGER DEFAULT 0,
                cached_at TEXT NOT NULL,
                expires_at TEXT
            )
        """)
        
        # Create indexes
        conn.execute("CREATE INDEX IF NOT EXISTS idx_runs_started_at ON cleanup_runs (started_at)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_runs_status ON cleanup_runs (status)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_candidates_run_id ON run_candidates (run_id)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_candidates_message_id ON run_candidates (message_id)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_cache_expires_at ON message_cache (expires_at)")
        
        conn.commit()
    
    # Profile operations
    def save_profile(self, profile: CleanupProfile) -> bool:
        """Save a cleanup profile."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                profile.updated_at = datetime.now()
                conn.execute("""
                    INSERT OR REPLACE INTO profiles 
                    (name, description, data, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    profile.name,
                    profile.description,
                    json.dumps(profile.to_dict()),
                    profile.created_at.isoformat(),
                    profile.updated_at.isoformat()
                ))
                conn.commit()
                logger.info(f"Saved profile: {profile.name}")
                return True
        except Exception as e:
            logger.error(f"Failed to save profile {profile.name}: {e}")
            return False
    
    def load_profile(self, name: str) -> Optional[CleanupProfile]:
        """Load a cleanup profile by name."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT data FROM profiles WHERE name = ?", (name,)
                )
                row = cursor.fetchone()
                if row:
                    data = json.loads(row[0])
                    return CleanupProfile.from_dict(data)
                return None
        except Exception as e:
            logger.error(f"Failed to load profile {name}: {e}")
            return None
    
    def list_profiles(self) -> List[CleanupProfile]:
        """List all cleanup profiles."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT data FROM profiles ORDER BY updated_at DESC"
                )
                profiles = []
                for row in cursor.fetchall():
                    data = json.loads(row[0])
                    profiles.append(CleanupProfile.from_dict(data))
                return profiles
        except Exception as e:
            logger.error(f"Failed to list profiles: {e}")
            return []
    
    def delete_profile(self, name: str) -> bool:
        """Delete a cleanup profile."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("DELETE FROM profiles WHERE name = ?", (name,))
                conn.commit()
                deleted = cursor.rowcount > 0
                if deleted:
                    logger.info(f"Deleted profile: {name}")
                return deleted
        except Exception as e:
            logger.error(f"Failed to delete profile {name}: {e}")
            return False
    
    # Run operations
    def save_run(self, run: CleanupRun) -> bool:
        """Save a cleanup run."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO cleanup_runs 
                    (run_id, profile_name, status, action, started_at, completed_at,
                     total_candidates, processed_count, success_count, error_count,
                     estimated_space_mb, actual_space_mb, error_messages)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    run.run_id,
                    run.profile_name,
                    run.status.value,
                    run.action.value,
                    run.started_at.isoformat(),
                    run.completed_at.isoformat() if run.completed_at else None,
                    run.total_candidates,
                    run.processed_count,
                    run.success_count,
                    run.error_count,
                    run.estimated_space_mb,
                    run.actual_space_mb,
                    json.dumps(run.error_messages)
                ))
                conn.commit()
                logger.debug(f"Saved run: {run.run_id}")
                return True
        except Exception as e:
            logger.error(f"Failed to save run {run.run_id}: {e}")
            return False
    
    def load_run(self, run_id: str) -> Optional[CleanupRun]:
        """Load a cleanup run by ID."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT run_id, profile_name, status, action, started_at, completed_at,
                           total_candidates, processed_count, success_count, error_count,
                           estimated_space_mb, actual_space_mb, error_messages
                    FROM cleanup_runs WHERE run_id = ?
                """, (run_id,))
                row = cursor.fetchone()
                if row:
                    return CleanupRun(
                        run_id=row[0],
                        profile_name=row[1],
                        status=RunStatus(row[2]),
                        action=row[3],
                        started_at=datetime.fromisoformat(row[4]),
                        completed_at=datetime.fromisoformat(row[5]) if row[5] else None,
                        total_candidates=row[6],
                        processed_count=row[7],
                        success_count=row[8],
                        error_count=row[9],
                        estimated_space_mb=row[10],
                        actual_space_mb=row[11],
                        error_messages=json.loads(row[12]) if row[12] else []
                    )
                return None
        except Exception as e:
            logger.error(f"Failed to load run {run_id}: {e}")
            return None
    
    def list_runs(self, limit: int = 100) -> List[CleanupRun]:
        """List recent cleanup runs."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT run_id, profile_name, status, action, started_at, completed_at,
                           total_candidates, processed_count, success_count, error_count,
                           estimated_space_mb, actual_space_mb, error_messages
                    FROM cleanup_runs 
                    ORDER BY started_at DESC 
                    LIMIT ?
                """, (limit,))
                
                runs = []
                for row in cursor.fetchall():
                    runs.append(CleanupRun(
                        run_id=row[0],
                        profile_name=row[1],
                        status=RunStatus(row[2]),
                        action=row[3],
                        started_at=datetime.fromisoformat(row[4]),
                        completed_at=datetime.fromisoformat(row[5]) if row[5] else None,
                        total_candidates=row[6],
                        processed_count=row[7],
                        success_count=row[8],
                        error_count=row[9],
                        estimated_space_mb=row[10],
                        actual_space_mb=row[11],
                        error_messages=json.loads(row[12]) if row[12] else []
                    ))
                return runs
        except Exception as e:
            logger.error(f"Failed to list runs: {e}")
            return []
    
    def save_run_candidates(self, run_id: str, candidates: List[CleanupCandidate]) -> bool:
        """Save candidates for a run."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Clear existing candidates for this run
                conn.execute("DELETE FROM run_candidates WHERE run_id = ?", (run_id,))
                
                # Insert new candidates
                for candidate in candidates:
                    conn.execute("""
                        INSERT INTO run_candidates 
                        (run_id, message_id, thread_id, subject, sender, date, size_bytes,
                         labels, category, matched_rules, reasons, action, excluded)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        run_id,
                        candidate.message.message_id,
                        candidate.message.thread_id,
                        candidate.message.subject,
                        candidate.message.sender,
                        candidate.message.date.isoformat(),
                        candidate.message.size_bytes,
                        json.dumps(candidate.message.labels),
                        candidate.message.category,
                        json.dumps(candidate.matched_rules),
                        json.dumps(candidate.reasons),
                        candidate.action.value,
                        1 if candidate.excluded else 0
                    ))
                
                conn.commit()
                logger.debug(f"Saved {len(candidates)} candidates for run {run_id}")
                return True
        except Exception as e:
            logger.error(f"Failed to save candidates for run {run_id}: {e}")
            return False
    
    def get_run_candidates(self, run_id: str) -> List[CleanupCandidate]:
        """Get candidates for a run."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT message_id, thread_id, subject, sender, date, size_bytes,
                           labels, category, matched_rules, reasons, action, excluded
                    FROM run_candidates WHERE run_id = ?
                    ORDER BY date DESC
                """, (run_id,))
                
                candidates = []
                for row in cursor.fetchall():
                    message = EmailMessage(
                        message_id=row[0],
                        thread_id=row[1],
                        subject=row[2],
                        sender=row[3],
                        date=datetime.fromisoformat(row[4]),
                        size_bytes=row[5],
                        labels=json.loads(row[6]) if row[6] else [],
                        category=row[7]
                    )
                    
                    candidate = CleanupCandidate(
                        message=message,
                        matched_rules=json.loads(row[8]) if row[8] else [],
                        reasons=json.loads(row[9]) if row[9] else [],
                        action=row[10],
                        excluded=bool(row[11])
                    )
                    candidates.append(candidate)
                
                return candidates
        except Exception as e:
            logger.error(f"Failed to get candidates for run {run_id}: {e}")
            return []
