"""
Gmail API client wrapper.

Provides high-level interface for Gmail operations with rate limiting and error handling.
"""

import logging
import time
import re
from typing import List, Dict, Any, Optional, Iterator, <PERSON><PERSON>
from datetime import datetime, timedelta
from email.utils import parsedate_to_datetime

from googleapiclient.errors import HttpError
from googleapiclient.discovery import Resource

from ..auth.oauth_manager import OAuthManager
from ..data.models import EmailMessage, FilterCriterion, FilterField, FilterOperator
from ..core.logging_config import log_api_call

logger = logging.getLogger(__name__)


class GmailAPIError(Exception):
    """Custom exception for Gmail API errors."""
    pass


class RateLimitExceeded(GmailAPIError):
    """Exception raised when API rate limits are exceeded."""
    pass


class GmailClient:
    """High-level Gmail API client with safety features."""
    
    # Gmail label mappings
    SYSTEM_LABELS = {
        'INBOX': 'INBOX',
        'SENT': 'SENT',
        'DRAFT': 'DRAFT',
        'SPAM': 'SPAM',
        'TRASH': 'TRASH',
        'STARRED': 'STARRED',
        'IMPORTANT': 'IMPORTANT',
        'UNREAD': 'UNREAD'
    }
    
    CATEGORY_LABELS = {
        'CATEGORY_PROMOTIONS': 'promotions',
        'CATEGORY_SOCIAL': 'social',
        'CATEGORY_UPDATES': 'updates',
        'CATEGORY_FORUMS': 'forums',
        'CATEGORY_PRIMARY': 'primary'
    }
    
    def __init__(self, oauth_manager: OAuthManager, rate_limit_delay: float = 0.1):
        """
        Initialize Gmail client.
        
        Args:
            oauth_manager: Authenticated OAuth manager
            rate_limit_delay: Delay between API calls in seconds
        """
        self.oauth_manager = oauth_manager
        self.rate_limit_delay = rate_limit_delay
        self._service: Optional[Resource] = None
        self._last_api_call = 0.0
        
        # Cache for labels
        self._labels_cache: Optional[Dict[str, str]] = None
        self._labels_cache_time: Optional[datetime] = None
    
    def _get_service(self) -> Resource:
        """Get authenticated Gmail service."""
        if not self._service:
            self._service = self.oauth_manager.get_gmail_service()
        return self._service
    
    def _rate_limit(self) -> None:
        """Apply rate limiting between API calls."""
        now = time.time()
        elapsed = now - self._last_api_call
        if elapsed < self.rate_limit_delay:
            time.sleep(self.rate_limit_delay - elapsed)
        self._last_api_call = time.time()
    
    def _handle_api_error(self, error: HttpError, operation: str) -> None:
        """Handle Gmail API errors with appropriate exceptions."""
        error_details = error.error_details if hasattr(error, 'error_details') else []
        
        if error.resp.status == 429:  # Rate limit exceeded
            log_api_call("Gmail", operation, "rate_limit_exceeded")
            raise RateLimitExceeded(f"Rate limit exceeded for {operation}")
        elif error.resp.status == 403:  # Forbidden
            log_api_call("Gmail", operation, "forbidden", {"error": str(error)})
            raise GmailAPIError(f"Access forbidden for {operation}: {error}")
        elif error.resp.status == 404:  # Not found
            log_api_call("Gmail", operation, "not_found", {"error": str(error)})
            raise GmailAPIError(f"Resource not found for {operation}: {error}")
        else:
            log_api_call("Gmail", operation, "error", {"error": str(error)})
            raise GmailAPIError(f"API error for {operation}: {error}")
    
    def get_profile(self) -> Dict[str, Any]:
        """Get user profile information."""
        try:
            self._rate_limit()
            service = self._get_service()
            profile = service.users().getProfile(userId='me').execute()
            
            log_api_call("Gmail", "users.getProfile", "success")
            return {
                'email': profile.get('emailAddress'),
                'messages_total': profile.get('messagesTotal', 0),
                'threads_total': profile.get('threadsTotal', 0),
                'history_id': profile.get('historyId')
            }
        except HttpError as e:
            self._handle_api_error(e, "users.getProfile")
    
    def get_labels(self, force_refresh: bool = False) -> Dict[str, str]:
        """
        Get Gmail labels mapping (ID -> name).
        
        Args:
            force_refresh: Force refresh of cached labels
            
        Returns:
            Dictionary mapping label IDs to names
        """
        # Check cache
        if (not force_refresh and self._labels_cache and self._labels_cache_time and 
            datetime.now() - self._labels_cache_time < timedelta(hours=1)):
            return self._labels_cache
        
        try:
            self._rate_limit()
            service = self._get_service()
            response = service.users().labels().list(userId='me').execute()
            
            labels = {}
            for label in response.get('labels', []):
                labels[label['id']] = label['name']
            
            # Cache the results
            self._labels_cache = labels
            self._labels_cache_time = datetime.now()
            
            log_api_call("Gmail", "users.labels.list", "success", {"count": len(labels)})
            return labels
            
        except HttpError as e:
            self._handle_api_error(e, "users.labels.list")
    
    def build_search_query(self, criteria: List[FilterCriterion]) -> str:
        """
        Build Gmail search query from filter criteria.
        
        Args:
            criteria: List of filter criteria
            
        Returns:
            Gmail search query string
        """
        query_parts = []
        
        for criterion in criteria:
            if criterion.field == FilterField.SENDER:
                if criterion.operator == FilterOperator.EQUALS:
                    query_parts.append(f'from:"{criterion.value}"')
                elif criterion.operator == FilterOperator.CONTAINS:
                    query_parts.append(f'from:{criterion.value}')
                elif criterion.operator == FilterOperator.REGEX:
                    # Gmail doesn't support regex directly, convert to contains
                    query_parts.append(f'from:{criterion.value}')
            
            elif criterion.field == FilterField.SUBJECT:
                if criterion.operator == FilterOperator.EQUALS:
                    query_parts.append(f'subject:"{criterion.value}"')
                elif criterion.operator == FilterOperator.CONTAINS:
                    query_parts.append(f'subject:{criterion.value}')
            
            elif criterion.field == FilterField.DATE:
                if criterion.operator == FilterOperator.GREATER_THAN:
                    # Parse date value
                    if isinstance(criterion.value, str):
                        if criterion.value.endswith('d'):
                            days = int(criterion.value[:-1])
                            query_parts.append(f'older_than:{days}d')
                        elif criterion.value.endswith('m'):
                            months = int(criterion.value[:-1])
                            query_parts.append(f'older_than:{months}m')
                        elif criterion.value.endswith('y'):
                            years = int(criterion.value[:-1])
                            query_parts.append(f'older_than:{years}y')
                        else:
                            # Assume it's a date string
                            query_parts.append(f'before:{criterion.value}')
            
            elif criterion.field == FilterField.SIZE:
                if criterion.operator == FilterOperator.GREATER_THAN:
                    # Convert bytes to MB for Gmail query
                    size_mb = int(criterion.value) // (1024 * 1024)
                    query_parts.append(f'larger:{size_mb}M')
            
            elif criterion.field == FilterField.CATEGORY:
                if criterion.operator == FilterOperator.EQUALS:
                    query_parts.append(f'category:{criterion.value}')
            
            elif criterion.field == FilterField.LABEL:
                if criterion.operator == FilterOperator.EQUALS:
                    query_parts.append(f'label:{criterion.value}')
                elif criterion.operator == FilterOperator.NOT_IN_LIST:
                    query_parts.append(f'-label:{criterion.value}')
            
            elif criterion.field == FilterField.STATUS:
                if criterion.value == 'unread':
                    query_parts.append('is:unread')
                elif criterion.value == 'read':
                    query_parts.append('-is:unread')
                elif criterion.value == 'starred':
                    query_parts.append('is:starred')
                elif criterion.value == 'important':
                    query_parts.append('is:important')
        
        return ' '.join(query_parts)
    
    def search_messages(self, query: str, max_results: int = 1000, 
                       page_token: Optional[str] = None) -> Tuple[List[str], Optional[str]]:
        """
        Search for messages using Gmail query.
        
        Args:
            query: Gmail search query
            max_results: Maximum number of results to return
            page_token: Token for pagination
            
        Returns:
            Tuple of (message_ids, next_page_token)
        """
        try:
            self._rate_limit()
            service = self._get_service()
            
            request_params = {
                'userId': 'me',
                'q': query,
                'maxResults': min(max_results, 500)  # Gmail API limit
            }
            
            if page_token:
                request_params['pageToken'] = page_token
            
            response = service.users().messages().list(**request_params).execute()
            
            messages = response.get('messages', [])
            message_ids = [msg['id'] for msg in messages]
            next_page_token = response.get('nextPageToken')
            
            log_api_call("Gmail", "users.messages.list", "success", {
                "query": query,
                "count": len(message_ids),
                "has_next_page": bool(next_page_token)
            })
            
            return message_ids, next_page_token
            
        except HttpError as e:
            self._handle_api_error(e, "users.messages.list")
    
    def get_message_metadata(self, message_id: str) -> EmailMessage:
        """
        Get message metadata.
        
        Args:
            message_id: Gmail message ID
            
        Returns:
            EmailMessage object with metadata
        """
        try:
            self._rate_limit()
            service = self._get_service()
            
            message = service.users().messages().get(
                userId='me',
                id=message_id,
                format='metadata',
                metadataHeaders=['From', 'Subject', 'Date', 'List-Unsubscribe', 
                               'Auto-Submitted', 'Precedence']
            ).execute()
            
            # Parse headers
            headers = {}
            for header in message.get('payload', {}).get('headers', []):
                headers[header['name'].lower()] = header['value']
            
            # Extract basic info
            subject = headers.get('subject', '(No Subject)')
            sender = headers.get('from', '(Unknown Sender)')
            date_str = headers.get('date', '')
            
            # Parse date
            try:
                if date_str:
                    date = parsedate_to_datetime(date_str)
                else:
                    date = datetime.now()
            except Exception:
                date = datetime.now()
            
            # Get labels and categories
            label_ids = message.get('labelIds', [])
            labels = []
            category = None
            
            for label_id in label_ids:
                if label_id in self.CATEGORY_LABELS:
                    category = self.CATEGORY_LABELS[label_id]
                else:
                    labels.append(label_id)
            
            # Check special flags
            is_starred = 'STARRED' in label_ids
            is_important = 'IMPORTANT' in label_ids
            is_unread = 'UNREAD' in label_ids
            
            # Get size estimate
            size_bytes = int(message.get('sizeEstimate', 0))
            
            email_message = EmailMessage(
                message_id=message_id,
                thread_id=message.get('threadId', ''),
                subject=subject,
                sender=sender,
                date=date,
                size_bytes=size_bytes,
                labels=labels,
                category=category,
                snippet=message.get('snippet', ''),
                headers=headers,
                is_starred=is_starred,
                is_important=is_important,
                is_unread=is_unread
            )
            
            log_api_call("Gmail", "users.messages.get", "success", {
                "message_id": message_id,
                "size_bytes": size_bytes
            })
            
            return email_message
            
        except HttpError as e:
            self._handle_api_error(e, "users.messages.get")
    
    def get_messages_batch(self, message_ids: List[str], 
                          batch_size: int = 100) -> Iterator[List[EmailMessage]]:
        """
        Get multiple messages in batches.
        
        Args:
            message_ids: List of message IDs to fetch
            batch_size: Number of messages per batch
            
        Yields:
            Batches of EmailMessage objects
        """
        for i in range(0, len(message_ids), batch_size):
            batch_ids = message_ids[i:i + batch_size]
            batch_messages = []
            
            for message_id in batch_ids:
                try:
                    message = self.get_message_metadata(message_id)
                    batch_messages.append(message)
                except GmailAPIError as e:
                    logger.warning(f"Failed to get message {message_id}: {e}")
                    continue
            
            yield batch_messages

    def trash_message(self, message_id: str) -> bool:
        """
        Move message to trash.

        Args:
            message_id: Gmail message ID

        Returns:
            True if successful, False otherwise
        """
        try:
            self._rate_limit()
            service = self._get_service()

            service.users().messages().trash(
                userId='me',
                id=message_id
            ).execute()

            log_api_call("Gmail", "users.messages.trash", "success", {
                "message_id": message_id
            })
            return True

        except HttpError as e:
            self._handle_api_error(e, "users.messages.trash")
            return False

    def untrash_message(self, message_id: str) -> bool:
        """
        Restore message from trash.

        Args:
            message_id: Gmail message ID

        Returns:
            True if successful, False otherwise
        """
        try:
            self._rate_limit()
            service = self._get_service()

            service.users().messages().untrash(
                userId='me',
                id=message_id
            ).execute()

            log_api_call("Gmail", "users.messages.untrash", "success", {
                "message_id": message_id
            })
            return True

        except HttpError as e:
            self._handle_api_error(e, "users.messages.untrash")
            return False

    def delete_message_permanent(self, message_id: str) -> bool:
        """
        Permanently delete message.

        Args:
            message_id: Gmail message ID

        Returns:
            True if successful, False otherwise
        """
        try:
            self._rate_limit()
            service = self._get_service()

            service.users().messages().delete(
                userId='me',
                id=message_id
            ).execute()

            log_api_call("Gmail", "users.messages.delete", "success", {
                "message_id": message_id
            })
            return True

        except HttpError as e:
            self._handle_api_error(e, "users.messages.delete")
            return False

    def modify_message_labels(self, message_id: str,
                             add_labels: List[str] = None,
                             remove_labels: List[str] = None) -> bool:
        """
        Modify message labels.

        Args:
            message_id: Gmail message ID
            add_labels: Labels to add
            remove_labels: Labels to remove

        Returns:
            True if successful, False otherwise
        """
        try:
            self._rate_limit()
            service = self._get_service()

            body = {}
            if add_labels:
                body['addLabelIds'] = add_labels
            if remove_labels:
                body['removeLabelIds'] = remove_labels

            if not body:
                return True  # Nothing to do

            service.users().messages().modify(
                userId='me',
                id=message_id,
                body=body
            ).execute()

            log_api_call("Gmail", "users.messages.modify", "success", {
                "message_id": message_id,
                "add_labels": add_labels,
                "remove_labels": remove_labels
            })
            return True

        except HttpError as e:
            self._handle_api_error(e, "users.messages.modify")
            return False

    def archive_message(self, message_id: str) -> bool:
        """
        Archive message (remove from INBOX).

        Args:
            message_id: Gmail message ID

        Returns:
            True if successful, False otherwise
        """
        return self.modify_message_labels(message_id, remove_labels=['INBOX'])

    def get_message_raw(self, message_id: str) -> Optional[str]:
        """
        Get raw message content for backup.

        Args:
            message_id: Gmail message ID

        Returns:
            Raw message content or None if failed
        """
        try:
            self._rate_limit()
            service = self._get_service()

            message = service.users().messages().get(
                userId='me',
                id=message_id,
                format='raw'
            ).execute()

            raw_content = message.get('raw', '')

            log_api_call("Gmail", "users.messages.get_raw", "success", {
                "message_id": message_id,
                "size": len(raw_content)
            })

            return raw_content

        except HttpError as e:
            self._handle_api_error(e, "users.messages.get_raw")
            return None
