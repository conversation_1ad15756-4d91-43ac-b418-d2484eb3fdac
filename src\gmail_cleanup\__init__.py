"""
Gmail Cleanup Tool - A GUI-based application for safely managing Gmail storage.

This package provides a comprehensive desktop application for identifying and
cleaning up unwanted Gmail messages with safety-first design principles.
"""

__version__ = "1.0.0"
__author__ = "Gmail Cleanup Tool"
__email__ = "<EMAIL>"

# Package metadata
__title__ = "Gmail Cleanup Tool"
__description__ = "A GUI-based Gmail cleanup tool for safely managing email storage"
__url__ = "https://github.com/example/gmail-cleanup-tool"
__license__ = "MIT"
