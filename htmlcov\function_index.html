<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">17%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-19 09:37 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_f4fb5d423fccdd22___init___py.html">src\gmail_cleanup\__init__.py</a></td>
                <td class="name left"><a href="z_f4fb5d423fccdd22___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf___init___py.html">src\gmail_cleanup\auth\__init__.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t34">src\gmail_cleanup\auth\oauth_manager.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t34"><data value='do_GET'>OAuthCallbackHandler.do_GET</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t81">src\gmail_cleanup\auth\oauth_manager.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t81"><data value='log_message'>OAuthCallbackHandler.log_message</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t93">src\gmail_cleanup\auth\oauth_manager.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t93"><data value='init__'>OAuthManager.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t102">src\gmail_cleanup\auth\oauth_manager.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t102"><data value='load_credentials'>OAuthManager._load_credentials</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t113">src\gmail_cleanup\auth\oauth_manager.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t113"><data value='save_credentials'>OAuthManager._save_credentials</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t130">src\gmail_cleanup\auth\oauth_manager.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t130"><data value='is_authenticated'>OAuthManager.is_authenticated</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t149">src\gmail_cleanup\auth\oauth_manager.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t149"><data value='get_required_scopes'>OAuthManager.get_required_scopes</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t158">src\gmail_cleanup\auth\oauth_manager.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t158"><data value='authenticate'>OAuthManager.authenticate</data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t259">src\gmail_cleanup\auth\oauth_manager.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t259"><data value='revoke_authentication'>OAuthManager.revoke_authentication</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t282">src\gmail_cleanup\auth\oauth_manager.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t282"><data value='get_gmail_service'>OAuthManager.get_gmail_service</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t293">src\gmail_cleanup\auth\oauth_manager.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t293"><data value='get_user_info'>OAuthManager.get_user_info</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t318">src\gmail_cleanup\auth\oauth_manager.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t318"><data value='test_connection'>OAuthManager.test_connection</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html">src\gmail_cleanup\auth\oauth_manager.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t36">src\gmail_cleanup\auth\token_storage.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t36"><data value='init__'>TokenStorage.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t44">src\gmail_cleanup\auth\token_storage.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t44"><data value='get_encryption_key'>TokenStorage._get_encryption_key</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t63">src\gmail_cleanup\auth\token_storage.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t63"><data value='encrypt_data'>TokenStorage._encrypt_data</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t69">src\gmail_cleanup\auth\token_storage.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t69"><data value='decrypt_data'>TokenStorage._decrypt_data</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t75">src\gmail_cleanup\auth\token_storage.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t75"><data value='save_credentials'>TokenStorage.save_credentials</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t121">src\gmail_cleanup\auth\token_storage.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t121"><data value='load_credentials'>TokenStorage.load_credentials</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t155">src\gmail_cleanup\auth\token_storage.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t155"><data value='clear_credentials'>TokenStorage.clear_credentials</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t192">src\gmail_cleanup\auth\token_storage.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t192"><data value='has_credentials'>TokenStorage.has_credentials</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t211">src\gmail_cleanup\auth\token_storage.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t211"><data value='get_storage_info'>TokenStorage.get_storage_info</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html">src\gmail_cleanup\auth\token_storage.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc___init___py.html">src\gmail_cleanup\core\__init__.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t19">src\gmail_cleanup\core\config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t19"><data value='init__'>AppConfig.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t31">src\gmail_cleanup\core\config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t31"><data value='get_default_config_dir'>AppConfig._get_default_config_dir</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t45">src\gmail_cleanup\core\config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t45"><data value='ensure_directories'>AppConfig._ensure_directories</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t59">src\gmail_cleanup\core\config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t59"><data value='load_config'>AppConfig._load_config</data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t73">src\gmail_cleanup\core\config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t73"><data value='get_default_settings'>AppConfig._get_default_settings</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t114">src\gmail_cleanup\core\config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t114"><data value='save_config'>AppConfig.save_config</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t123">src\gmail_cleanup\core\config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t123"><data value='get'>AppConfig.get</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t136">src\gmail_cleanup\core\config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t136"><data value='set'>AppConfig.set</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t150">src\gmail_cleanup\core\config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t150"><data value='get_config_dir'>AppConfig.get_config_dir</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t154">src\gmail_cleanup\core\config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t154"><data value='get_data_dir'>AppConfig.get_data_dir</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t158">src\gmail_cleanup\core\config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t158"><data value='get_logs_dir'>AppConfig.get_logs_dir</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t162">src\gmail_cleanup\core\config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t162"><data value='get_backups_dir'>AppConfig.get_backups_dir</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t166">src\gmail_cleanup\core\config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t166"><data value='get_exports_dir'>AppConfig.get_exports_dir</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t170">src\gmail_cleanup\core\config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t170"><data value='get_profiles_dir'>AppConfig.get_profiles_dir</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t174">src\gmail_cleanup\core\config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t174"><data value='get_database_path'>AppConfig.get_database_path</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html">src\gmail_cleanup\core\config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html#t18">src\gmail_cleanup\core\logging_config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html#t18"><data value='format'>JSONFormatter.format</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html#t44">src\gmail_cleanup\core\logging_config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html#t44"><data value='init__'>HumanFormatter.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html#t51">src\gmail_cleanup\core\logging_config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html#t51"><data value='setup_logging'>setup_logging</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html#t98">src\gmail_cleanup\core\logging_config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html#t98"><data value='get_activity_logger'>get_activity_logger</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html#t104">src\gmail_cleanup\core\logging_config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html#t104"><data value='log_user_action'>log_user_action</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html#t122">src\gmail_cleanup\core\logging_config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html#t122"><data value='log_api_call'>log_api_call</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html">src\gmail_cleanup\core\logging_config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece___init___py.html">src\gmail_cleanup\data\__init__.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t24">src\gmail_cleanup\data\database.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t24"><data value='init__'>DatabaseManager.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t29">src\gmail_cleanup\data\database.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t29"><data value='ensure_database'>DatabaseManager._ensure_database</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t38">src\gmail_cleanup\data\database.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t38"><data value='create_tables'>DatabaseManager._create_tables</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t127">src\gmail_cleanup\data\database.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t127"><data value='save_profile'>DatabaseManager.save_profile</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t150">src\gmail_cleanup\data\database.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t150"><data value='load_profile'>DatabaseManager.load_profile</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t166">src\gmail_cleanup\data\database.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t166"><data value='list_profiles'>DatabaseManager.list_profiles</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t182">src\gmail_cleanup\data\database.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t182"><data value='delete_profile'>DatabaseManager.delete_profile</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t197">src\gmail_cleanup\data\database.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t197"><data value='save_run'>DatabaseManager.save_run</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t229">src\gmail_cleanup\data\database.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t229"><data value='load_run'>DatabaseManager.load_run</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t261">src\gmail_cleanup\data\database.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t261"><data value='list_runs'>DatabaseManager.list_runs</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t296">src\gmail_cleanup\data\database.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t296"><data value='save_run_candidates'>DatabaseManager.save_run_candidates</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t333">src\gmail_cleanup\data\database.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t333"><data value='get_run_candidates'>DatabaseManager.get_run_candidates</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html">src\gmail_cleanup\data\database.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t65">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t65"><data value='to_dict'>FilterCriterion.to_dict</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t75">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t75"><data value='from_dict'>FilterCriterion.from_dict</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t93">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t93"><data value='to_dict'>FilterRule.to_dict</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t103">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t103"><data value='from_dict'>FilterRule.from_dict</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t129">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t129"><data value='to_dict'>SafetySettings.to_dict</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t134">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t134"><data value='from_dict'>SafetySettings.from_dict</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t151">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t151"><data value='to_dict'>CleanupProfile.to_dict</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t165">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t165"><data value='from_dict'>CleanupProfile.from_dict</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t196">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t196"><data value='to_dict'>EmailMessage.to_dict</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t215">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t215"><data value='from_dict'>EmailMessage.from_dict</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t243">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t243"><data value='to_dict'>CleanupCandidate.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t254">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t254"><data value='from_dict'>CleanupCandidate.from_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t282">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t282"><data value='to_dict'>CleanupRun.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t301">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t301"><data value='from_dict'>CleanupRun.from_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>125</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="125 125">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3___init___py.html">src\gmail_cleanup\gui\__init__.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t36">src\gmail_cleanup\gui\main_window.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t36"><data value='init__'>MainWindow.__init__</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t49">src\gmail_cleanup\gui\main_window.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t49"><data value='setup_ui'>MainWindow._setup_ui</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t84">src\gmail_cleanup\gui\main_window.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t84"><data value='create_sidebar'>MainWindow._create_sidebar</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t159">src\gmail_cleanup\gui\main_window.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t159"><data value='setup_tabs'>MainWindow._setup_tabs</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t179">src\gmail_cleanup\gui\main_window.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t179"><data value='setup_connections'>MainWindow._setup_connections</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t186">src\gmail_cleanup\gui\main_window.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t186"><data value='on_nav_selection_changed'>MainWindow._on_nav_selection_changed</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t192">src\gmail_cleanup\gui\main_window.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t192"><data value='switch_to_tab'>MainWindow._switch_to_tab</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t213">src\gmail_cleanup\gui\main_window.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t213"><data value='restore_geometry'>MainWindow._restore_geometry</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t223">src\gmail_cleanup\gui\main_window.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t223"><data value='closeEvent'>MainWindow.closeEvent</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t247">src\gmail_cleanup\gui\main_window.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t247"><data value='show_error'>MainWindow.show_error</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t251">src\gmail_cleanup\gui\main_window.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t251"><data value='show_warning'>MainWindow.show_warning</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t255">src\gmail_cleanup\gui\main_window.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t255"><data value='show_info'>MainWindow.show_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t259">src\gmail_cleanup\gui\main_window.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t259"><data value='get_current_tab'>MainWindow.get_current_tab</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t263">src\gmail_cleanup\gui\main_window.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t263"><data value='switch_to_tab'>MainWindow.switch_to_tab</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html">src\gmail_cleanup\gui\main_window.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e___init___py.html">src\gmail_cleanup\gui\tabs\__init__.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_backups_reports_py.html#t22">src\gmail_cleanup\gui\tabs\backups_reports.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_backups_reports_py.html#t22"><data value='init__'>BackupsReportsTab.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_backups_reports_py.html#t25">src\gmail_cleanup\gui\tabs\backups_reports.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_backups_reports_py.html#t25"><data value='setup_ui'>BackupsReportsTab._setup_ui</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_backups_reports_py.html">src\gmail_cleanup\gui\tabs\backups_reports.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_backups_reports_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t28">src\gmail_cleanup\gui\tabs\base_tab.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t28"><data value='init__'>BaseTab.__init__</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t46">src\gmail_cleanup\gui\tabs\base_tab.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t46"><data value='setup_ui'>BaseTab._setup_ui</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t58">src\gmail_cleanup\gui\tabs\base_tab.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t58"><data value='setup_connections'>BaseTab._setup_connections</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t69">src\gmail_cleanup\gui\tabs\base_tab.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t69"><data value='refresh_data'>BaseTab.refresh_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t73">src\gmail_cleanup\gui\tabs\base_tab.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t73"><data value='on_tab_activated'>BaseTab.on_tab_activated</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t78">src\gmail_cleanup\gui\tabs\base_tab.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t78"><data value='on_tab_deactivated'>BaseTab.on_tab_deactivated</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t82">src\gmail_cleanup\gui\tabs\base_tab.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t82"><data value='start_auto_refresh'>BaseTab.start_auto_refresh</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t87">src\gmail_cleanup\gui\tabs\base_tab.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t87"><data value='stop_auto_refresh'>BaseTab.stop_auto_refresh</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t92">src\gmail_cleanup\gui\tabs\base_tab.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t92"><data value='emit_status'>BaseTab.emit_status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t96">src\gmail_cleanup\gui\tabs\base_tab.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t96"><data value='emit_error'>BaseTab.emit_error</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t101">src\gmail_cleanup\gui\tabs\base_tab.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t101"><data value='emit_warning'>BaseTab.emit_warning</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t106">src\gmail_cleanup\gui\tabs\base_tab.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t106"><data value='emit_info'>BaseTab.emit_info</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t111">src\gmail_cleanup\gui\tabs\base_tab.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t111"><data value='is_authenticated'>BaseTab.is_authenticated</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t116">src\gmail_cleanup\gui\tabs\base_tab.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t116"><data value='require_authentication'>BaseTab.require_authentication</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html">src\gmail_cleanup\gui\tabs\base_tab.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_cleanup_py.html#t22">src\gmail_cleanup\gui\tabs\cleanup.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_cleanup_py.html#t22"><data value='init__'>CleanupTab.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_cleanup_py.html#t25">src\gmail_cleanup\gui\tabs\cleanup.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_cleanup_py.html#t25"><data value='setup_ui'>CleanupTab._setup_ui</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_cleanup_py.html#t37">src\gmail_cleanup\gui\tabs\cleanup.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_cleanup_py.html#t37"><data value='is_operation_in_progress'>CleanupTab.is_operation_in_progress</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_cleanup_py.html">src\gmail_cleanup\gui\tabs\cleanup.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_cleanup_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_criteria_builder_py.html#t22">src\gmail_cleanup\gui\tabs\criteria_builder.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_criteria_builder_py.html#t22"><data value='init__'>CriteriaBuilderTab.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_criteria_builder_py.html#t25">src\gmail_cleanup\gui\tabs\criteria_builder.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_criteria_builder_py.html#t25"><data value='setup_ui'>CriteriaBuilderTab._setup_ui</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_criteria_builder_py.html">src\gmail_cleanup\gui\tabs\criteria_builder.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_criteria_builder_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t26">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t26"><data value='init__'>StatCard.__init__</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t60">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t60"><data value='update_value'>StatCard.update_value</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t76">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t76"><data value='init__'>DashboardTab.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t86">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t86"><data value='setup_ui'>DashboardTab._setup_ui</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t108">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t108"><data value='create_account_section'>DashboardTab._create_account_section</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t129">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t129"><data value='create_stats_section'>DashboardTab._create_stats_section</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t149">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t149"><data value='create_actions_section'>DashboardTab._create_actions_section</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t219">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t219"><data value='create_activity_section'>DashboardTab._create_activity_section</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t233">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t233"><data value='setup_connections'>DashboardTab._setup_connections</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t241">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t241"><data value='on_tab_changed'>DashboardTab._on_tab_changed</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t246">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t246"><data value='on_run_preview'>DashboardTab._on_run_preview</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t255">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t255"><data value='on_run_cleanup'>DashboardTab._on_run_cleanup</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t264">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t264"><data value='on_open_logs'>DashboardTab._on_open_logs</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t269">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t269"><data value='on_open_backups'>DashboardTab._on_open_backups</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t274">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t274"><data value='refresh_data'>DashboardTab.refresh_data</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t290">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t290"><data value='update_account_status'>DashboardTab._update_account_status</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t302">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t302"><data value='update_statistics'>DashboardTab._update_statistics</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t312">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t312"><data value='update_recent_activity'>DashboardTab._update_recent_activity</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t324">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t324"><data value='update_button_states'>DashboardTab._update_button_states</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_logs_py.html#t22">src\gmail_cleanup\gui\tabs\logs.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_logs_py.html#t22"><data value='init__'>LogsTab.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_logs_py.html#t25">src\gmail_cleanup\gui\tabs\logs.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_logs_py.html#t25"><data value='setup_ui'>LogsTab._setup_ui</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_logs_py.html">src\gmail_cleanup\gui\tabs\logs.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_logs_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_preview_py.html#t22">src\gmail_cleanup\gui\tabs\preview.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_preview_py.html#t22"><data value='init__'>PreviewTab.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_preview_py.html#t25">src\gmail_cleanup\gui\tabs\preview.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_preview_py.html#t25"><data value='setup_ui'>PreviewTab._setup_ui</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_preview_py.html">src\gmail_cleanup\gui\tabs\preview.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_preview_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_settings_py.html#t22">src\gmail_cleanup\gui\tabs\settings.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_settings_py.html#t22"><data value='init__'>SettingsTab.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_settings_py.html#t25">src\gmail_cleanup\gui\tabs\settings.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_settings_py.html#t25"><data value='setup_ui'>SettingsTab._setup_ui</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_settings_py.html">src\gmail_cleanup\gui\tabs\settings.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_settings_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f4fb5d423fccdd22_main_py.html#t21">src\gmail_cleanup\main.py</a></td>
                <td class="name left"><a href="z_f4fb5d423fccdd22_main_py.html#t21"><data value='setup_application'>setup_application</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f4fb5d423fccdd22_main_py.html#t41">src\gmail_cleanup\main.py</a></td>
                <td class="name left"><a href="z_f4fb5d423fccdd22_main_py.html#t41"><data value='main'>main</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f4fb5d423fccdd22_main_py.html">src\gmail_cleanup\main.py</a></td>
                <td class="name left"><a href="z_f4fb5d423fccdd22_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>2</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1207</td>
                <td>1001</td>
                <td>2</td>
                <td class="right" data-ratio="206 1207">17%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-19 09:37 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
