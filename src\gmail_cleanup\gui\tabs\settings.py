"""
Settings tab for the Gmail Cleanup Tool.

Provides configuration interface for OAuth, preferences, and application settings.
"""

import logging
from typing import Optional

from PySide6.QtWidgets import (
    QLabel, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLineEdit,
    QPushButton, QSpinBox, QCheckBox, QTextEdit, QFormLayout,
    QMessageBox, QProgressBar, QComboBox
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont

from .base_tab import BaseTab
from ...core.config import AppConfig
from ...auth.oauth_manager import OAuthManager

logger = logging.getLogger(__name__)


class AuthenticationWorker(QThread):
    """Worker thread for OAuth authentication."""

    authentication_complete = Signal(bool, str)  # success, message

    def __init__(self, oauth_manager: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, client_id: str, client_secret: str):
        super().__init__()
        self.oauth_manager = oauth_manager
        self.client_id = client_id
        self.client_secret = client_secret

    def run(self):
        """Run authentication in background thread."""
        try:
            success = self.oauth_manager.authenticate(self.client_id, self.client_secret)
            if success:
                self.authentication_complete.emit(True, "Authentication successful!")
            else:
                self.authentication_complete.emit(False, "Authentication failed.")
        except Exception as e:
            self.authentication_complete.emit(False, f"Authentication error: {e}")


class SettingsTab(BaseTab):
    """Settings tab for application configuration."""

    def __init__(self, config: AppConfig, parent: Optional[QWidget] = None):
        super().__init__(config, parent)
        self.oauth_manager = OAuthManager(config)
        self.auth_worker = None

        # Update timer for connection status
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_connection_status)
        self.status_timer.start(5000)  # Update every 5 seconds

    def _setup_ui(self) -> None:
        """Set up the settings UI."""
        # Title
        title = QLabel("Settings")
        title.setFont(QFont("Arial", 20, QFont.Bold))
        title.setStyleSheet("color: #333; margin-bottom: 10px;")
        self.layout.addWidget(title)

        # OAuth Configuration Section
        self._create_oauth_section()

        # Safety Settings Section
        self._create_safety_section()

        # Performance Settings Section
        self._create_performance_section()

        # Application Settings Section
        self._create_app_section()

        self.layout.addStretch()

    def _create_oauth_section(self) -> None:
        """Create OAuth configuration section."""
        group = QGroupBox("Gmail Authentication")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QFormLayout(group)

        # Connection status
        self.connection_status = QLabel("Checking...")
        self.connection_status.setStyleSheet("font-weight: bold;")
        layout.addRow("Status:", self.connection_status)

        # Client ID
        self.client_id_edit = QLineEdit()
        self.client_id_edit.setPlaceholderText("Enter your OAuth Client ID")
        self.client_id_edit.setText(self.config.get("oauth.client_id", ""))
        layout.addRow("Client ID:", self.client_id_edit)

        # Client Secret
        self.client_secret_edit = QLineEdit()
        self.client_secret_edit.setPlaceholderText("Enter your OAuth Client Secret")
        self.client_secret_edit.setEchoMode(QLineEdit.Password)
        self.client_secret_edit.setText(self.config.get("oauth.client_secret", ""))
        layout.addRow("Client Secret:", self.client_secret_edit)

        # Buttons
        button_layout = QHBoxLayout()

        self.authenticate_btn = QPushButton("Authenticate")
        self.authenticate_btn.clicked.connect(self._authenticate)
        button_layout.addWidget(self.authenticate_btn)

        self.test_connection_btn = QPushButton("Test Connection")
        self.test_connection_btn.clicked.connect(self._test_connection)
        button_layout.addWidget(self.test_connection_btn)

        self.revoke_btn = QPushButton("Revoke Access")
        self.revoke_btn.clicked.connect(self._revoke_authentication)
        button_layout.addWidget(self.revoke_btn)

        button_layout.addStretch()
        layout.addRow("", button_layout)

        # Progress bar
        self.auth_progress = QProgressBar()
        self.auth_progress.setVisible(False)
        layout.addRow("", self.auth_progress)

        # Instructions
        instructions = QTextEdit()
        instructions.setMaximumHeight(100)
        instructions.setReadOnly(True)
        instructions.setPlainText(
            "1. Create OAuth credentials in Google Cloud Console\n"
            "2. Enable Gmail API for your project\n"
            "3. Enter Client ID and Secret above\n"
            "4. Click 'Authenticate' and follow browser prompts"
        )
        layout.addRow("Instructions:", instructions)

        self.layout.addWidget(group)

    def _create_safety_section(self) -> None:
        """Create safety settings section."""
        group = QGroupBox("Safety Settings")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QVBoxLayout(group)

        # Skip options
        self.skip_starred_cb = QCheckBox("Skip starred messages")
        self.skip_starred_cb.setChecked(self.config.get("safety.skip_starred", True))
        layout.addWidget(self.skip_starred_cb)

        self.skip_important_cb = QCheckBox("Skip important messages")
        self.skip_important_cb.setChecked(self.config.get("safety.skip_important", True))
        layout.addWidget(self.skip_important_cb)

        self.skip_contacts_cb = QCheckBox("Skip messages from contacts")
        self.skip_contacts_cb.setChecked(self.config.get("safety.skip_from_contacts", True))
        layout.addWidget(self.skip_contacts_cb)

        # Thread mode
        thread_layout = QHBoxLayout()
        thread_layout.addWidget(QLabel("Thread processing:"))
        self.thread_mode_combo = QComboBox()
        self.thread_mode_combo.addItems(["Process individual messages", "Process entire threads"])
        current_mode = self.config.get("safety.thread_mode", "message")
        self.thread_mode_combo.setCurrentIndex(0 if current_mode == "message" else 1)
        thread_layout.addWidget(self.thread_mode_combo)
        thread_layout.addStretch()
        layout.addLayout(thread_layout)

        self.layout.addWidget(group)

    def _create_performance_section(self) -> None:
        """Create performance settings section."""
        group = QGroupBox("Performance & Quotas")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QFormLayout(group)

        # Batch size
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(10, 500)
        self.batch_size_spin.setValue(self.config.get("quotas.batch_size", 100))
        layout.addRow("Batch size:", self.batch_size_spin)

        # Delay
        self.delay_spin = QSpinBox()
        self.delay_spin.setRange(100, 10000)
        self.delay_spin.setSuffix(" ms")
        self.delay_spin.setValue(self.config.get("quotas.per_batch_delay_ms", 1000))
        layout.addRow("Delay between batches:", self.delay_spin)

        # Max messages
        self.max_messages_spin = QSpinBox()
        self.max_messages_spin.setRange(100, 100000)
        self.max_messages_spin.setValue(self.config.get("quotas.max_messages_per_run", 10000))
        layout.addRow("Max messages per run:", self.max_messages_spin)

        self.layout.addWidget(group)

    def _create_app_section(self) -> None:
        """Create application settings section."""
        group = QGroupBox("Application Settings")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QFormLayout(group)

        # Theme
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["System", "Light", "Dark"])
        current_theme = self.config.get("ui.theme", "system")
        theme_index = {"system": 0, "light": 1, "dark": 2}.get(current_theme, 0)
        self.theme_combo.setCurrentIndex(theme_index)
        layout.addRow("Theme:", self.theme_combo)

        # Log level
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        current_level = self.config.get("logging.level", "INFO")
        self.log_level_combo.setCurrentText(current_level)
        layout.addRow("Log level:", self.log_level_combo)

        # Save button
        save_btn = QPushButton("Save Settings")
        save_btn.clicked.connect(self._save_settings)
        layout.addRow("", save_btn)

        self.layout.addWidget(group)

    def _update_connection_status(self) -> None:
        """Update the connection status display."""
        try:
            if self.oauth_manager.is_authenticated():
                user_info = self.oauth_manager.get_user_info()
                if user_info:
                    email = user_info.get('email', 'Unknown')
                    self.connection_status.setText(f"✅ Connected as {email}")
                    self.connection_status.setStyleSheet("color: green; font-weight: bold;")
                else:
                    self.connection_status.setText("⚠️ Connected but unable to get user info")
                    self.connection_status.setStyleSheet("color: orange; font-weight: bold;")
            else:
                self.connection_status.setText("❌ Not connected")
                self.connection_status.setStyleSheet("color: red; font-weight: bold;")
        except Exception as e:
            self.connection_status.setText(f"❌ Error: {e}")
            self.connection_status.setStyleSheet("color: red; font-weight: bold;")

    def _authenticate(self) -> None:
        """Start OAuth authentication process."""
        client_id = self.client_id_edit.text().strip()
        client_secret = self.client_secret_edit.text().strip()

        if not client_id or not client_secret:
            self.emit_warning("Missing Credentials",
                            "Please enter both Client ID and Client Secret.")
            return

        # Save credentials to config
        self.config.set("oauth.client_id", client_id)
        self.config.set("oauth.client_secret", client_secret)

        # Start authentication in background thread
        self.authenticate_btn.setEnabled(False)
        self.auth_progress.setVisible(True)
        self.auth_progress.setRange(0, 0)  # Indeterminate progress

        self.auth_worker = AuthenticationWorker(self.oauth_manager, client_id, client_secret)
        self.auth_worker.authentication_complete.connect(self._on_authentication_complete)
        self.auth_worker.start()

        self.emit_status("Starting OAuth authentication...")

    def _on_authentication_complete(self, success: bool, message: str) -> None:
        """Handle authentication completion."""
        self.authenticate_btn.setEnabled(True)
        self.auth_progress.setVisible(False)

        if success:
            self.emit_info("Authentication Successful", message)
            self._update_connection_status()
        else:
            self.emit_error("Authentication Failed", message)

        self.emit_status("Authentication process completed")

    def _test_connection(self) -> None:
        """Test the current connection."""
        try:
            if self.oauth_manager.test_connection():
                user_info = self.oauth_manager.get_user_info()
                if user_info:
                    email = user_info.get('email', 'Unknown')
                    messages_total = user_info.get('messages_total', 0)
                    self.emit_info("Connection Test Successful",
                                 f"Connected as {email}\nTotal messages: {messages_total:,}")
                else:
                    self.emit_warning("Connection Test", "Connected but unable to get user info")
            else:
                self.emit_error("Connection Test Failed", "Unable to connect to Gmail API")
        except Exception as e:
            self.emit_error("Connection Test Error", str(e))

    def _revoke_authentication(self) -> None:
        """Revoke current authentication."""
        reply = QMessageBox.question(
            self,
            "Revoke Authentication",
            "Are you sure you want to revoke Gmail access?\n\n"
            "You will need to re-authenticate to use the application.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                if self.oauth_manager.revoke_authentication():
                    self.emit_info("Authentication Revoked",
                                 "Gmail access has been revoked successfully.")
                    self._update_connection_status()
                else:
                    self.emit_warning("Revoke Failed",
                                    "Failed to revoke authentication, but local tokens cleared.")
            except Exception as e:
                self.emit_error("Revoke Error", str(e))

    def _save_settings(self) -> None:
        """Save all settings to configuration."""
        try:
            # Save OAuth settings
            self.config.set("oauth.client_id", self.client_id_edit.text().strip())
            self.config.set("oauth.client_secret", self.client_secret_edit.text().strip())

            # Save safety settings
            self.config.set("safety.skip_starred", self.skip_starred_cb.isChecked())
            self.config.set("safety.skip_important", self.skip_important_cb.isChecked())
            self.config.set("safety.skip_from_contacts", self.skip_contacts_cb.isChecked())

            thread_mode = "message" if self.thread_mode_combo.currentIndex() == 0 else "thread"
            self.config.set("safety.thread_mode", thread_mode)

            # Save performance settings
            self.config.set("quotas.batch_size", self.batch_size_spin.value())
            self.config.set("quotas.per_batch_delay_ms", self.delay_spin.value())
            self.config.set("quotas.max_messages_per_run", self.max_messages_spin.value())

            # Save app settings
            theme_map = {0: "system", 1: "light", 2: "dark"}
            self.config.set("ui.theme", theme_map[self.theme_combo.currentIndex()])
            self.config.set("logging.level", self.log_level_combo.currentText())

            self.emit_info("Settings Saved", "All settings have been saved successfully.")
            self.emit_status("Settings saved")

        except Exception as e:
            self.emit_error("Save Error", f"Failed to save settings: {e}")

    def is_authenticated(self) -> bool:
        """Check if user is authenticated."""
        return self.oauth_manager.is_authenticated()

    def on_tab_activated(self) -> None:
        """Called when tab becomes active."""
        super().on_tab_activated()
        self._update_connection_status()
