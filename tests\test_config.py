"""
Tests for configuration management.
"""

import pytest
import tempfile
from pathlib import Path

from gmail_cleanup.core.config import AppConfig


class TestAppConfig:
    """Test the AppConfig class."""
    
    def test_config_initialization(self):
        """Test that config initializes with default values."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = AppConfig(Path(temp_dir))
            
            # Check that default settings are loaded
            assert config.get("version") == "1.0.0"
            assert config.get("safety.skip_starred") is True
            assert config.get("safety.skip_important") is True
            assert config.get("quotas.batch_size") == 100
    
    def test_config_get_set(self):
        """Test getting and setting configuration values."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = AppConfig(Path(temp_dir))
            
            # Test setting and getting values
            config.set("test.value", "hello")
            assert config.get("test.value") == "hello"
            
            # Test nested values
            config.set("nested.deep.value", 42)
            assert config.get("nested.deep.value") == 42
            
            # Test default values
            assert config.get("nonexistent.key", "default") == "default"
    
    def test_config_persistence(self):
        """Test that configuration persists across instances."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir)
            
            # Create first config instance and set a value
            config1 = AppConfig(config_dir)
            config1.set("persistent.value", "test123")
            
            # Create second config instance and check value persists
            config2 = AppConfig(config_dir)
            assert config2.get("persistent.value") == "test123"
    
    def test_directory_creation(self):
        """Test that required directories are created."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = AppConfig(Path(temp_dir))
            
            # Check that all required directories exist
            assert config.get_config_dir().exists()
            assert config.get_data_dir().exists()
            assert config.get_logs_dir().exists()
            assert config.get_backups_dir().exists()
            assert config.get_exports_dir().exists()
            assert config.get_profiles_dir().exists()
    
    def test_database_path(self):
        """Test database path generation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = AppConfig(Path(temp_dir))
            db_path = config.get_database_path()
            
            assert db_path.name == "gmail_cleanup.db"
            assert db_path.parent == config.get_data_dir()
