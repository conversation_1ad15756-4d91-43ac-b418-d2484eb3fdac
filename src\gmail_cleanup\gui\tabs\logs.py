"""
Logs tab for the Gmail Cleanup Tool.

Provides log viewing interface with filtering and export capabilities.
"""

import logging
import json
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from pathlib import Path

from PySide6.QtWidgets import (
    QLabel, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QTextEdit, QComboBox,
    QLineEdit, QDateTimeEdit, QCheckBox, QSplitter, QTabWidget,
    QFileDialog, QMessageBox, QProgressBar, QFormLayout, QSpinBox
)
from PySide6.QtCore import Qt, QDateTime, QTimer, QThread, Signal, QObject
from PySide6.QtGui import QFont, QTextCursor

from .base_tab import BaseTab
from ...core.config import AppConfig

logger = logging.getLogger(__name__)


class LogReader(QThread):
    """Thread for reading log files without blocking the UI."""

    logs_loaded = Signal(list)  # List of log entries
    progress_updated = Signal(int, int)  # current, total
    error_occurred = Signal(str)

    def __init__(self, log_file: Path, max_lines: int = 1000):
        super().__init__()
        self.log_file = log_file
        self.max_lines = max_lines

    def run(self):
        """Read log file in background."""
        try:
            if not self.log_file.exists():
                self.logs_loaded.emit([])
                return

            logs = []

            if self.log_file.suffix == '.jsonl':
                # Read JSONL structured logs
                with open(self.log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                total_lines = min(len(lines), self.max_lines)
                # Read from the end for most recent logs
                start_line = max(0, len(lines) - self.max_lines)

                for i, line in enumerate(lines[start_line:], 1):
                    self.progress_updated.emit(i, total_lines)

                    try:
                        log_entry = json.loads(line.strip())
                        logs.append(log_entry)
                    except json.JSONDecodeError:
                        continue
            else:
                # Read plain text logs
                with open(self.log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                total_lines = min(len(lines), self.max_lines)
                start_line = max(0, len(lines) - self.max_lines)

                for i, line in enumerate(lines[start_line:], 1):
                    self.progress_updated.emit(i, total_lines)

                    # Parse plain text log line
                    log_entry = self._parse_plain_log_line(line.strip())
                    if log_entry:
                        logs.append(log_entry)

            # Sort by timestamp (newest first)
            logs.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            self.logs_loaded.emit(logs)

        except Exception as e:
            self.error_occurred.emit(str(e))

    def _parse_plain_log_line(self, line: str) -> Optional[Dict[str, Any]]:
        """Parse a plain text log line into structured format."""
        if not line:
            return None

        try:
            # Expected format: "2024-01-15 10:30:45 - INFO - module_name - message"
            parts = line.split(' - ', 3)
            if len(parts) >= 4:
                timestamp_str, level, logger_name, message = parts
                return {
                    'timestamp': timestamp_str,
                    'level': level,
                    'logger': logger_name,
                    'message': message,
                    'module': logger_name.split('.')[-1] if '.' in logger_name else logger_name,
                    'function': '',
                    'line': 0
                }
        except Exception:
            pass

        # Fallback: treat entire line as message
        return {
            'timestamp': datetime.now().isoformat(),
            'level': 'INFO',
            'logger': 'unknown',
            'message': line,
            'module': 'unknown',
            'function': '',
            'line': 0
        }


class LogsTab(BaseTab):
    """Logs tab for viewing application logs."""

    def __init__(self, config: AppConfig, parent: Optional[QWidget] = None):
        # Initialize attributes first
        self.config = config
        self.logs_dir = config.get_logs_dir()
        self.current_logs: List[Dict[str, Any]] = []
        self.filtered_logs: List[Dict[str, Any]] = []
        self.log_reader = None

        # Auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self._auto_refresh)

        # Then call parent constructor
        super().__init__(config, parent)

    def _setup_ui(self) -> None:
        """Set up the logs UI."""
        # Title
        title = QLabel("Application Logs")
        title.setFont(QFont("Arial", 20, QFont.Bold))
        title.setStyleSheet("color: #333; margin-bottom: 10px;")
        self.layout.addWidget(title)

        # Create tab widget for different log views
        tab_widget = QTabWidget()

        # Structured logs tab
        self._create_structured_logs_tab(tab_widget)

        # Live logs tab
        self._create_live_logs_tab(tab_widget)

        # Log management tab
        self._create_log_management_tab(tab_widget)

        self.layout.addWidget(tab_widget)

        # Start auto-refresh
        self.refresh_timer.start(10000)  # Refresh every 10 seconds

    def _create_structured_logs_tab(self, parent) -> None:
        """Create the structured logs viewing tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Controls
        controls_group = QGroupBox("Log Filters")
        controls_layout = QFormLayout(controls_group)

        # Log file selector
        file_layout = QHBoxLayout()
        self.log_file_combo = QComboBox()
        self._refresh_log_files()
        file_layout.addWidget(self.log_file_combo)

        refresh_files_btn = QPushButton("🔄")
        refresh_files_btn.setMaximumWidth(30)
        refresh_files_btn.clicked.connect(self._refresh_log_files)
        file_layout.addWidget(refresh_files_btn)

        controls_layout.addRow("Log File:", file_layout)

        # Level filter
        self.level_filter_combo = QComboBox()
        self.level_filter_combo.addItems(["All Levels", "DEBUG", "INFO", "WARNING", "ERROR"])
        self.level_filter_combo.currentTextChanged.connect(self._apply_filters)
        controls_layout.addRow("Level:", self.level_filter_combo)

        # Module filter
        self.module_filter_edit = QLineEdit()
        self.module_filter_edit.setPlaceholderText("Filter by module name...")
        self.module_filter_edit.textChanged.connect(self._apply_filters)
        controls_layout.addRow("Module:", self.module_filter_edit)

        # Message filter
        self.message_filter_edit = QLineEdit()
        self.message_filter_edit.setPlaceholderText("Search in messages...")
        self.message_filter_edit.textChanged.connect(self._apply_filters)
        controls_layout.addRow("Message:", self.message_filter_edit)

        # Time range
        time_layout = QHBoxLayout()

        self.time_filter_cb = QCheckBox("Filter by time:")
        time_layout.addWidget(self.time_filter_cb)

        self.start_time_edit = QDateTimeEdit()
        self.start_time_edit.setDateTime(QDateTime.currentDateTime().addDays(-1))
        self.start_time_edit.setEnabled(False)
        time_layout.addWidget(self.start_time_edit)

        time_layout.addWidget(QLabel("to"))

        self.end_time_edit = QDateTimeEdit()
        self.end_time_edit.setDateTime(QDateTime.currentDateTime())
        self.end_time_edit.setEnabled(False)
        time_layout.addWidget(self.end_time_edit)

        self.time_filter_cb.toggled.connect(self.start_time_edit.setEnabled)
        self.time_filter_cb.toggled.connect(self.end_time_edit.setEnabled)
        self.time_filter_cb.toggled.connect(self._apply_filters)
        self.start_time_edit.dateTimeChanged.connect(self._apply_filters)
        self.end_time_edit.dateTimeChanged.connect(self._apply_filters)

        time_layout.addStretch()
        controls_layout.addRow("Time Range:", time_layout)

        # Action buttons
        action_layout = QHBoxLayout()

        load_btn = QPushButton("📂 Load Logs")
        load_btn.clicked.connect(self._load_logs)
        action_layout.addWidget(load_btn)

        export_btn = QPushButton("💾 Export Filtered")
        export_btn.clicked.connect(self._export_logs)
        action_layout.addWidget(export_btn)

        clear_btn = QPushButton("🗑️ Clear Display")
        clear_btn.clicked.connect(self._clear_logs)
        action_layout.addWidget(clear_btn)

        action_layout.addStretch()
        controls_layout.addRow("Actions:", action_layout)

        layout.addWidget(controls_group)

        # Progress bar
        self.load_progress = QProgressBar()
        self.load_progress.setVisible(False)
        layout.addWidget(self.load_progress)

        # Splitter for table and details
        splitter = QSplitter(Qt.Vertical)

        # Logs table
        self.logs_table = QTableWidget()
        self.logs_table.setColumnCount(6)
        self.logs_table.setHorizontalHeaderLabels([
            "Timestamp", "Level", "Module", "Function", "Line", "Message"
        ])

        # Configure table
        header = self.logs_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Timestamp
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Level
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Module
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Function
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Line
        header.setSectionResizeMode(5, QHeaderView.Stretch)           # Message

        self.logs_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.logs_table.setAlternatingRowColors(True)
        self.logs_table.setSortingEnabled(True)
        self.logs_table.itemSelectionChanged.connect(self._on_log_selected)

        splitter.addWidget(self.logs_table)

        # Log details
        details_group = QGroupBox("Log Entry Details")
        details_layout = QVBoxLayout(details_group)

        self.log_details_text = QTextEdit()
        self.log_details_text.setReadOnly(True)
        self.log_details_text.setMaximumHeight(150)
        self.log_details_text.setFont(QFont("Courier New", 9))
        details_layout.addWidget(self.log_details_text)

        splitter.addWidget(details_group)
        splitter.setSizes([400, 150])

        layout.addWidget(splitter)

        parent.addTab(tab, "📋 Structured Logs")

    def _create_live_logs_tab(self, parent) -> None:
        """Create the live logs viewing tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Controls
        controls_layout = QHBoxLayout()

        self.auto_scroll_cb = QCheckBox("Auto-scroll")
        self.auto_scroll_cb.setChecked(True)
        controls_layout.addWidget(self.auto_scroll_cb)

        self.live_level_combo = QComboBox()
        self.live_level_combo.addItems(["All Levels", "INFO", "WARNING", "ERROR"])
        self.live_level_combo.setCurrentText("INFO")
        controls_layout.addWidget(self.live_level_combo)

        pause_btn = QPushButton("⏸️ Pause")
        pause_btn.setCheckable(True)
        pause_btn.toggled.connect(self._toggle_live_updates)
        controls_layout.addWidget(pause_btn)

        clear_live_btn = QPushButton("🗑️ Clear")
        clear_live_btn.clicked.connect(self._clear_live_logs)
        controls_layout.addWidget(clear_live_btn)

        controls_layout.addStretch()
        layout.addLayout(controls_layout)

        # Live log display
        self.live_logs_text = QTextEdit()
        self.live_logs_text.setReadOnly(True)
        self.live_logs_text.setFont(QFont("Courier New", 9))
        self.live_logs_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #555;
            }
        """)
        layout.addWidget(self.live_logs_text)

        parent.addTab(tab, "📺 Live Logs")

        # Set up live log monitoring
        self._setup_live_logging()

    def _create_log_management_tab(self, parent) -> None:
        """Create the log management tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Log directory info
        info_group = QGroupBox("Log Directory Information")
        info_layout = QFormLayout(info_group)

        info_layout.addRow("Log Directory:", QLabel(str(self.logs_dir)))

        # Calculate log directory size
        total_size = 0
        file_count = 0
        if self.logs_dir.exists():
            for file_path in self.logs_dir.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
                    file_count += 1

        size_mb = total_size / (1024 * 1024)
        info_layout.addRow("Total Size:", QLabel(f"{size_mb:.1f} MB"))
        info_layout.addRow("File Count:", QLabel(f"{file_count} files"))

        layout.addWidget(info_group)

        # Log settings
        settings_group = QGroupBox("Log Settings")
        settings_layout = QFormLayout(settings_group)

        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        current_level = self.config.get("logging.level", "INFO")
        self.log_level_combo.setCurrentText(current_level)
        settings_layout.addRow("Log Level:", self.log_level_combo)

        self.max_log_files_spin = QSpinBox()
        self.max_log_files_spin.setRange(1, 100)
        self.max_log_files_spin.setValue(self.config.get("logging.max_log_files", 10))
        settings_layout.addRow("Max Log Files:", self.max_log_files_spin)

        self.max_log_size_spin = QSpinBox()
        self.max_log_size_spin.setRange(1, 100)
        self.max_log_size_spin.setSuffix(" MB")
        self.max_log_size_spin.setValue(self.config.get("logging.max_log_size_mb", 10))
        settings_layout.addRow("Max Log Size:", self.max_log_size_spin)

        save_settings_btn = QPushButton("💾 Save Settings")
        save_settings_btn.clicked.connect(self._save_log_settings)
        settings_layout.addRow("", save_settings_btn)

        layout.addWidget(settings_group)

        # Log management actions
        actions_group = QGroupBox("Log Management")
        actions_layout = QVBoxLayout(actions_group)

        actions_buttons = QHBoxLayout()

        archive_logs_btn = QPushButton("📦 Archive Old Logs")
        archive_logs_btn.clicked.connect(self._archive_old_logs)
        actions_buttons.addWidget(archive_logs_btn)

        delete_old_logs_btn = QPushButton("🗑️ Delete Old Logs")
        delete_old_logs_btn.clicked.connect(self._delete_old_logs)
        actions_buttons.addWidget(delete_old_logs_btn)

        open_log_dir_btn = QPushButton("📁 Open Log Directory")
        open_log_dir_btn.clicked.connect(self._open_log_directory)
        actions_buttons.addWidget(open_log_dir_btn)

        actions_buttons.addStretch()
        actions_layout.addLayout(actions_buttons)

        layout.addWidget(actions_group)

        layout.addStretch()

        parent.addTab(tab, "⚙️ Log Management")

    def _refresh_log_files(self) -> None:
        """Refresh the list of available log files."""
        self.log_file_combo.clear()

        if not self.logs_dir.exists():
            return

        # Find log files
        log_files = []
        for pattern in ["*.log", "*.jsonl"]:
            log_files.extend(self.logs_dir.glob(pattern))

        # Sort by modification time (newest first)
        log_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)

        for log_file in log_files:
            display_name = f"{log_file.name} ({self._format_file_size(log_file.stat().st_size)})"
            self.log_file_combo.addItem(display_name)
            self.log_file_combo.setItemData(self.log_file_combo.count() - 1, log_file)

    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human-readable format."""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"

    def _load_logs(self) -> None:
        """Load logs from the selected file."""
        if self.log_file_combo.count() == 0:
            self.emit_warning("No Log Files", "No log files found in the logs directory.")
            return

        # Get selected log file
        index = self.log_file_combo.currentIndex()
        if index < 0:
            return

        log_file = self.log_file_combo.itemData(index)
        if not log_file or not log_file.exists():
            self.emit_error("File Not Found", "Selected log file does not exist.")
            return

        # Start loading in background
        self.log_reader = LogReader(log_file, max_lines=5000)
        self.log_reader.logs_loaded.connect(self._on_logs_loaded)
        self.log_reader.progress_updated.connect(self._on_load_progress)
        self.log_reader.error_occurred.connect(self._on_load_error)

        self.load_progress.setVisible(True)
        self.load_progress.setRange(0, 100)
        self.load_progress.setValue(0)

        self.log_reader.start()

    def _on_logs_loaded(self, logs: List[Dict[str, Any]]) -> None:
        """Handle logs loaded from file."""
        self.current_logs = logs
        self.load_progress.setVisible(False)
        self._apply_filters()

        self.emit_status(f"Loaded {len(logs)} log entries")

    def _on_load_progress(self, current: int, total: int) -> None:
        """Handle load progress updates."""
        if total > 0:
            progress = int((current / total) * 100)
            self.load_progress.setValue(progress)

    def _on_load_error(self, error: str) -> None:
        """Handle load errors."""
        self.load_progress.setVisible(False)
        self.emit_error("Load Error", f"Failed to load logs: {error}")

    def _apply_filters(self) -> None:
        """Apply current filters to the logs."""
        if not self.current_logs:
            return

        filtered = self.current_logs.copy()

        # Level filter
        level_filter = self.level_filter_combo.currentText()
        if level_filter != "All Levels":
            filtered = [log for log in filtered if log.get('level') == level_filter]

        # Module filter
        module_filter = self.module_filter_edit.text().lower()
        if module_filter:
            filtered = [log for log in filtered
                       if module_filter in log.get('module', '').lower() or
                          module_filter in log.get('logger', '').lower()]

        # Message filter
        message_filter = self.message_filter_edit.text().lower()
        if message_filter:
            filtered = [log for log in filtered
                       if message_filter in log.get('message', '').lower()]

        # Time filter
        if self.time_filter_cb.isChecked():
            start_time = self.start_time_edit.dateTime().toPython()
            end_time = self.end_time_edit.dateTime().toPython()

            filtered = [log for log in filtered
                       if self._is_log_in_time_range(log, start_time, end_time)]

        self.filtered_logs = filtered
        self._populate_logs_table()

    def _is_log_in_time_range(self, log: Dict[str, Any], start_time: datetime, end_time: datetime) -> bool:
        """Check if log entry is within time range."""
        try:
            timestamp_str = log.get('timestamp', '')
            if 'T' in timestamp_str:
                # ISO format
                log_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            else:
                # Simple format
                log_time = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')

            return start_time <= log_time <= end_time
        except Exception:
            return True  # Include if we can't parse the timestamp

    def _populate_logs_table(self) -> None:
        """Populate the logs table with filtered data."""
        self.logs_table.setRowCount(len(self.filtered_logs))

        for row, log in enumerate(self.filtered_logs):
            # Timestamp
            timestamp = log.get('timestamp', '')
            if 'T' in timestamp:
                # Format ISO timestamp
                try:
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    timestamp = dt.strftime('%Y-%m-%d %H:%M:%S')
                except Exception:
                    pass

            timestamp_item = QTableWidgetItem(timestamp)
            self.logs_table.setItem(row, 0, timestamp_item)

            # Level
            level = log.get('level', '')
            level_item = QTableWidgetItem(level)

            # Color code by level
            if level == 'ERROR':
                level_item.setBackground(Qt.lightCoral)
            elif level == 'WARNING':
                level_item.setBackground(Qt.yellow)
            elif level == 'DEBUG':
                level_item.setBackground(Qt.lightGray)

            self.logs_table.setItem(row, 1, level_item)

            # Module
            module_item = QTableWidgetItem(log.get('module', ''))
            self.logs_table.setItem(row, 2, module_item)

            # Function
            function_item = QTableWidgetItem(log.get('function', ''))
            self.logs_table.setItem(row, 3, function_item)

            # Line
            line_item = QTableWidgetItem(str(log.get('line', '')))
            self.logs_table.setItem(row, 4, line_item)

            # Message
            message_item = QTableWidgetItem(log.get('message', ''))
            self.logs_table.setItem(row, 5, message_item)

            # Store full log data
            timestamp_item.setData(Qt.UserRole, log)

    def _on_log_selected(self) -> None:
        """Handle log entry selection."""
        current_row = self.logs_table.currentRow()
        if current_row < 0:
            self.log_details_text.clear()
            return

        # Get log data
        timestamp_item = self.logs_table.item(current_row, 0)
        if timestamp_item:
            log_data = timestamp_item.data(Qt.UserRole)
            if log_data:
                self._display_log_details(log_data)

    def _display_log_details(self, log_data: Dict[str, Any]) -> None:
        """Display detailed information about a log entry."""
        details_text = f"""
Timestamp: {log_data.get('timestamp', 'N/A')}
Level: {log_data.get('level', 'N/A')}
Logger: {log_data.get('logger', 'N/A')}
Module: {log_data.get('module', 'N/A')}
Function: {log_data.get('function', 'N/A')}
Line: {log_data.get('line', 'N/A')}

Message:
{log_data.get('message', 'N/A')}
"""

        # Add extra fields if present
        extra_fields = {k: v for k, v in log_data.items()
                       if k not in ['timestamp', 'level', 'logger', 'module', 'function', 'line', 'message']}

        if extra_fields:
            details_text += f"\nExtra Fields:\n"
            for key, value in extra_fields.items():
                details_text += f"{key}: {value}\n"

        self.log_details_text.setPlainText(details_text.strip())

    def _clear_logs(self) -> None:
        """Clear the logs display."""
        self.current_logs = []
        self.filtered_logs = []
        self.logs_table.setRowCount(0)
        self.log_details_text.clear()
        self.emit_status("Logs cleared")

    def _export_logs(self) -> None:
        """Export filtered logs to file."""
        if not self.filtered_logs:
            self.emit_warning("No Data", "No logs to export.")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Export Logs",
            f"logs_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON Files (*.json);;Text Files (*.txt);;All Files (*)"
        )

        if file_path:
            try:
                if file_path.endswith('.json'):
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(self.filtered_logs, f, indent=2, ensure_ascii=False)
                else:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        for log in self.filtered_logs:
                            f.write(f"[{log.get('timestamp', '')}] {log.get('level', '')} - {log.get('module', '')} - {log.get('message', '')}\n")

                self.emit_info("Export Successful", f"Logs exported to {file_path}")

            except Exception as e:
                self.emit_error("Export Failed", f"Failed to export logs: {e}")

    # Live logging methods
    def _setup_live_logging(self) -> None:
        """Set up live log monitoring."""
        # For now, just show a placeholder message
        self.live_logs_text.append("Live logging will be implemented in a future version.")
        self.live_logs_text.append("Use the Structured Logs tab to view application logs.")

    def _add_live_log(self, message: str) -> None:
        """Add a message to the live display."""
        if hasattr(self, 'live_logs_text'):
            timestamp = datetime.now().strftime('%H:%M:%S')
            log_line = f'[{timestamp}] {message}'
            self.live_logs_text.append(log_line)

            # Auto-scroll if enabled
            if self.auto_scroll_cb.isChecked():
                scrollbar = self.live_logs_text.verticalScrollBar()
                scrollbar.setValue(scrollbar.maximum())

    def _toggle_live_updates(self, paused: bool) -> None:
        """Toggle live log updates."""
        # Placeholder for live updates toggle
        status = "paused" if paused else "resumed"
        self._add_live_log(f"Live updates {status}")

    def _clear_live_logs(self) -> None:
        """Clear the live logs display."""
        self.live_logs_text.clear()

    def _auto_refresh(self) -> None:
        """Auto-refresh logs if enabled."""
        # Only refresh if we're on the structured logs tab and have a file selected
        if hasattr(self, 'logs_table') and self.log_file_combo.count() > 0:
            # Don't auto-refresh if user is actively working with the logs
            if not self.logs_table.hasFocus() and not self.log_details_text.hasFocus():
                self._load_logs()

    # Log management methods
    def _save_log_settings(self) -> None:
        """Save log settings to configuration."""
        try:
            self.config.set("logging.level", self.log_level_combo.currentText())
            self.config.set("logging.max_log_files", self.max_log_files_spin.value())
            self.config.set("logging.max_log_size_mb", self.max_log_size_spin.value())

            self.emit_info("Settings Saved", "Log settings have been saved. Restart the application for changes to take effect.")

        except Exception as e:
            self.emit_error("Save Error", f"Failed to save log settings: {e}")

    def _archive_old_logs(self) -> None:
        """Archive old log files."""
        self.emit_info("Archive Logs", "Log archiving will be implemented in a future version.")

    def _delete_old_logs(self) -> None:
        """Delete old log files."""
        if not self.logs_dir.exists():
            self.emit_info("No Logs", "No log directory found.")
            return

        reply = QMessageBox.question(
            self,
            "Delete Old Logs",
            "This will delete log files older than 30 days.\n\nAre you sure you want to continue?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                cutoff_date = datetime.now() - timedelta(days=30)
                deleted_count = 0

                for log_file in self.logs_dir.glob("*.log*"):
                    if log_file.is_file():
                        file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                        if file_time < cutoff_date:
                            log_file.unlink()
                            deleted_count += 1

                self.emit_info("Delete Complete", f"Deleted {deleted_count} old log files.")
                self._refresh_log_files()

            except Exception as e:
                self.emit_error("Delete Failed", f"Failed to delete old logs: {e}")

    def _open_log_directory(self) -> None:
        """Open the log directory in file explorer."""
        self.logs_dir.mkdir(parents=True, exist_ok=True)

        import subprocess
        import platform

        try:
            if platform.system() == "Windows":
                subprocess.run(["explorer", str(self.logs_dir)])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", str(self.logs_dir)])
            else:  # Linux
                subprocess.run(["xdg-open", str(self.logs_dir)])
        except Exception as e:
            self.emit_error("Open Directory", f"Failed to open log directory: {e}")

    def on_tab_activated(self) -> None:
        """Called when tab becomes active."""
        super().on_tab_activated()
        self._refresh_log_files()


# LiveLogHandler removed - using simplified approach for live logs
