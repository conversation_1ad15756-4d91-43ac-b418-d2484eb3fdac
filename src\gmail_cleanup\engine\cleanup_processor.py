"""
Cleanup processor for orchestrating Gmail cleanup operations.

Handles the complete workflow from rule evaluation to message processing.
"""

import logging
import uuid
from typing import List, Dict, Any, Optional, Iterator, Callable
from datetime import datetime

from ..api.gmail_client import Gmail<PERSON>lient, GmailAPIError, RateLimitExceeded
from ..data.models import (
    CleanupProfile, CleanupRun, CleanupCandidate, EmailMessage,
    RunStatus, CleanupAction
)
from ..data.database import DatabaseManager
from .rule_engine import RuleEngine
from ..core.logging_config import log_user_action

logger = logging.getLogger(__name__)


class CleanupProcessor:
    """Orchestrates Gmail cleanup operations."""
    
    def __init__(self, gmail_client: GmailClient, database: DatabaseManager):
        """
        Initialize cleanup processor.
        
        Args:
            gmail_client: Authenticated Gmail client
            database: Database manager for persistence
        """
        self.gmail_client = gmail_client
        self.database = database
        
        # Progress callbacks
        self.progress_callback: Optional[Callable[[str, int, int], None]] = None
        self.status_callback: Optional[Callable[[str], None]] = None
        
        # Current run state
        self.current_run: Optional[CleanupRun] = None
        self.is_cancelled = False
    
    def set_progress_callback(self, callback: Callable[[str, int, int], None]) -> None:
        """Set callback for progress updates (message, current, total)."""
        self.progress_callback = callback
    
    def set_status_callback(self, callback: Callable[[str], None]) -> None:
        """Set callback for status updates."""
        self.status_callback = callback
    
    def _emit_progress(self, message: str, current: int, total: int) -> None:
        """Emit progress update."""
        if self.progress_callback:
            self.progress_callback(message, current, total)
    
    def _emit_status(self, status: str) -> None:
        """Emit status update."""
        if self.status_callback:
            self.status_callback(status)
        logger.info(f"Cleanup status: {status}")
    
    def preview_cleanup(self, profile: CleanupProfile, max_messages: int = 1000) -> CleanupRun:
        """
        Run a preview of cleanup operation without making changes.
        
        Args:
            profile: Cleanup profile to use
            max_messages: Maximum number of messages to evaluate
            
        Returns:
            CleanupRun with preview results
        """
        run_id = str(uuid.uuid4())
        run = CleanupRun(
            run_id=run_id,
            profile_name=profile.name,
            status=RunStatus.RUNNING,
            action=CleanupAction.TRASH,  # Preview only
            started_at=datetime.now()
        )
        
        self.current_run = run
        self.is_cancelled = False
        
        try:
            self._emit_status("Starting preview...")
            log_user_action("preview_start", {"profile": profile.name, "max_messages": max_messages})
            
            # Initialize rule engine
            rule_engine = RuleEngine(profile.safety_settings)
            
            # Build search query from rules
            search_query = self._build_search_query(profile)
            self._emit_status(f"Searching with query: {search_query}")
            
            # Search for candidate messages
            candidates = []
            total_found = 0
            
            message_ids, next_page_token = self.gmail_client.search_messages(
                search_query, max_results=max_messages
            )
            total_found = len(message_ids)
            
            self._emit_progress("Evaluating messages...", 0, total_found)
            
            # Process messages in batches
            processed = 0
            for batch in self.gmail_client.get_messages_batch(message_ids, batch_size=50):
                if self.is_cancelled:
                    break
                
                for message in batch:
                    if self.is_cancelled:
                        break
                    
                    # Evaluate message against rules
                    candidate = rule_engine.evaluate_message(message, profile.rules)
                    
                    # Apply size filter if specified
                    if profile.size_filter_mb:
                        size_threshold = profile.size_filter_mb * 1024 * 1024
                        if message.size_bytes < size_threshold:
                            candidate.excluded = True
                            candidate.reasons.append(f"Size below {profile.size_filter_mb}MB threshold")
                    
                    candidates.append(candidate)
                    processed += 1
                    
                    self._emit_progress("Evaluating messages...", processed, total_found)
            
            # Calculate statistics
            active_candidates = [c for c in candidates if not c.excluded]
            estimated_space = sum(c.message.size_bytes for c in active_candidates) / (1024 * 1024)
            
            # Update run
            run.total_candidates = len(active_candidates)
            run.processed_count = processed
            run.estimated_space_mb = estimated_space
            run.status = RunStatus.COMPLETED if not self.is_cancelled else RunStatus.CANCELLED
            run.completed_at = datetime.now()
            
            # Save to database
            self.database.save_run(run)
            self.database.save_run_candidates(run_id, candidates)
            
            self._emit_status(f"Preview completed: {len(active_candidates)} candidates found")
            log_user_action("preview_complete", {
                "profile": profile.name,
                "candidates": len(active_candidates),
                "estimated_space_mb": estimated_space
            })
            
            return run
            
        except Exception as e:
            logger.error(f"Preview failed: {e}")
            run.status = RunStatus.FAILED
            run.error_messages.append(str(e))
            run.completed_at = datetime.now()
            self.database.save_run(run)
            
            self._emit_status(f"Preview failed: {e}")
            raise
        
        finally:
            self.current_run = None
    
    def execute_cleanup(self, profile: CleanupProfile, candidates: List[CleanupCandidate],
                       batch_size: int = 100, delay_ms: int = 1000) -> CleanupRun:
        """
        Execute actual cleanup operation.
        
        Args:
            profile: Cleanup profile to use
            candidates: List of candidates to process
            batch_size: Number of messages to process per batch
            delay_ms: Delay between batches in milliseconds
            
        Returns:
            CleanupRun with execution results
        """
        run_id = str(uuid.uuid4())
        run = CleanupRun(
            run_id=run_id,
            profile_name=profile.name,
            status=RunStatus.RUNNING,
            action=profile.action,
            started_at=datetime.now(),
            total_candidates=len([c for c in candidates if not c.excluded])
        )
        
        self.current_run = run
        self.is_cancelled = False
        
        try:
            self._emit_status("Starting cleanup execution...")
            log_user_action("cleanup_start", {
                "profile": profile.name,
                "action": profile.action.value,
                "candidates": run.total_candidates
            })
            
            # Filter to active candidates only
            active_candidates = [c for c in candidates if not c.excluded]
            
            processed = 0
            success_count = 0
            error_count = 0
            errors = []
            
            self._emit_progress("Processing messages...", 0, len(active_candidates))
            
            # Process in batches
            for i in range(0, len(active_candidates), batch_size):
                if self.is_cancelled:
                    break
                
                batch = active_candidates[i:i + batch_size]
                
                for candidate in batch:
                    if self.is_cancelled:
                        break
                    
                    try:
                        success = self._process_message(candidate.message, profile.action)
                        if success:
                            success_count += 1
                        else:
                            error_count += 1
                            errors.append(f"Failed to process message {candidate.message.message_id}")
                    
                    except Exception as e:
                        error_count += 1
                        error_msg = f"Error processing {candidate.message.message_id}: {e}"
                        errors.append(error_msg)
                        logger.error(error_msg)
                    
                    processed += 1
                    self._emit_progress("Processing messages...", processed, len(active_candidates))
                
                # Delay between batches
                if delay_ms > 0 and i + batch_size < len(active_candidates):
                    import time
                    time.sleep(delay_ms / 1000.0)
            
            # Calculate actual space reclaimed (estimate)
            successful_candidates = active_candidates[:success_count]
            actual_space = sum(c.message.size_bytes for c in successful_candidates) / (1024 * 1024)
            
            # Update run
            run.processed_count = processed
            run.success_count = success_count
            run.error_count = error_count
            run.actual_space_mb = actual_space
            run.error_messages = errors[:10]  # Keep only first 10 errors
            run.status = RunStatus.COMPLETED if not self.is_cancelled else RunStatus.CANCELLED
            run.completed_at = datetime.now()
            
            # Save to database
            self.database.save_run(run)
            
            self._emit_status(f"Cleanup completed: {success_count} processed, {error_count} errors")
            log_user_action("cleanup_complete", {
                "profile": profile.name,
                "success_count": success_count,
                "error_count": error_count,
                "actual_space_mb": actual_space
            })
            
            return run
            
        except Exception as e:
            logger.error(f"Cleanup execution failed: {e}")
            run.status = RunStatus.FAILED
            run.error_messages.append(str(e))
            run.completed_at = datetime.now()
            self.database.save_run(run)
            
            self._emit_status(f"Cleanup failed: {e}")
            raise
        
        finally:
            self.current_run = None
    
    def _build_search_query(self, profile: CleanupProfile) -> str:
        """Build Gmail search query from profile rules."""
        if not profile.rules:
            return "in:inbox"  # Default to inbox if no rules
        
        # Use the first rule to build the query
        # For more complex scenarios, we might need to combine multiple rules
        if profile.rules:
            return self.gmail_client.build_search_query(profile.rules[0].criteria)
        
        return "in:inbox"
    
    def _process_message(self, message: EmailMessage, action: CleanupAction) -> bool:
        """
        Process a single message with the specified action.
        
        Args:
            message: Message to process
            action: Action to perform
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if action == CleanupAction.TRASH:
                return self.gmail_client.trash_message(message.message_id)
            elif action == CleanupAction.ARCHIVE:
                return self.gmail_client.archive_message(message.message_id)
            elif action == CleanupAction.DELETE_PERMANENT:
                return self.gmail_client.delete_message_permanent(message.message_id)
            elif action == CleanupAction.LABEL_ONLY:
                # Add a cleanup label
                return self.gmail_client.modify_message_labels(
                    message.message_id, 
                    add_labels=['CLEANUP_PROCESSED']
                )
            else:
                logger.warning(f"Unknown action: {action}")
                return False
                
        except (GmailAPIError, RateLimitExceeded) as e:
            logger.error(f"API error processing message {message.message_id}: {e}")
            return False
    
    def cancel_operation(self) -> None:
        """Cancel the current operation."""
        self.is_cancelled = True
        self._emit_status("Cancelling operation...")
        log_user_action("operation_cancelled")
