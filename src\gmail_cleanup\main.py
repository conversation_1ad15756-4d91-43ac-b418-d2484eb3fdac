"""
Main entry point for the Gmail Cleanup Tool.

This module initializes the application and starts the GUI.
"""

import sys
import logging
from pathlib import Path
from typing import Optional

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt, QDir
from PySide6.QtGui import QIcon

from .gui.main_window import MainWindow
from .core.config import AppConfig
from .core.logging_config import setup_logging


def setup_application() -> QApplication:
    """Set up the Qt application with proper configuration."""
    # Enable high DPI scaling
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    app = QApplication(sys.argv)
    app.setApplicationName("Gmail Cleanup Tool")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Gmail Cleanup Tool")
    app.setOrganizationDomain("example.com")
    
    # Set application icon if available
    icon_path = Path(__file__).parent / "resources" / "icons" / "app_icon.png"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    return app


def main() -> int:
    """Main entry point for the application."""
    try:
        # Set up application
        app = setup_application()
        
        # Initialize configuration
        config = AppConfig()
        
        # Set up logging
        setup_logging(config.get_logs_dir())
        logger = logging.getLogger(__name__)
        logger.info("Starting Gmail Cleanup Tool v1.0.0")
        
        # Create and show main window
        main_window = MainWindow(config)
        main_window.show()
        
        # Start event loop
        return app.exec()
        
    except Exception as e:
        # Fallback error handling
        print(f"Fatal error starting application: {e}", file=sys.stderr)
        return 1


if __name__ == "__main__":
    sys.exit(main())
