<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">7%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-19 10:04 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_f4fb5d423fccdd22___init___py.html">src\gmail_cleanup\__init__.py</a></td>
                <td class="name left"><a href="z_f4fb5d423fccdd22___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aa9d318d4a813f3c___init___py.html">src\gmail_cleanup\api\__init__.py</a></td>
                <td class="name left"><a href="z_aa9d318d4a813f3c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aa9d318d4a813f3c_gmail_client_py.html#t24">src\gmail_cleanup\api\gmail_client.py</a></td>
                <td class="name left"><a href="z_aa9d318d4a813f3c_gmail_client_py.html#t24"><data value='GmailAPIError'>GmailAPIError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aa9d318d4a813f3c_gmail_client_py.html#t29">src\gmail_cleanup\api\gmail_client.py</a></td>
                <td class="name left"><a href="z_aa9d318d4a813f3c_gmail_client_py.html#t29"><data value='RateLimitExceeded'>RateLimitExceeded</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aa9d318d4a813f3c_gmail_client_py.html#t34">src\gmail_cleanup\api\gmail_client.py</a></td>
                <td class="name left"><a href="z_aa9d318d4a813f3c_gmail_client_py.html#t34"><data value='GmailClient'>GmailClient</data></a></td>
                <td>209</td>
                <td>209</td>
                <td>0</td>
                <td class="right" data-ratio="0 209">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aa9d318d4a813f3c_gmail_client_py.html">src\gmail_cleanup\api\gmail_client.py</a></td>
                <td class="name left"><a href="z_aa9d318d4a813f3c_gmail_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf___init___py.html">src\gmail_cleanup\auth\__init__.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t31">src\gmail_cleanup\auth\oauth_manager.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t31"><data value='OAuthCallbackHandler'>OAuthCallbackHandler</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t86">src\gmail_cleanup\auth\oauth_manager.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html#t86"><data value='OAuthManager'>OAuthManager</data></a></td>
                <td>121</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="0 121">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html">src\gmail_cleanup\auth\oauth_manager.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t30">src\gmail_cleanup\auth\token_storage.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html#t30"><data value='TokenStorage'>TokenStorage</data></a></td>
                <td>104</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="0 104">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html">src\gmail_cleanup\auth\token_storage.py</a></td>
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc___init___py.html">src\gmail_cleanup\core\__init__.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t16">src\gmail_cleanup\core\config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html#t16"><data value='AppConfig'>AppConfig</data></a></td>
                <td>55</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="42 55">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html">src\gmail_cleanup\core\config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html#t15">src\gmail_cleanup\core\logging_config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html#t15"><data value='JSONFormatter'>JSONFormatter</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html#t41">src\gmail_cleanup\core\logging_config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html#t41"><data value='HumanFormatter'>HumanFormatter</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html">src\gmail_cleanup\core\logging_config.py</a></td>
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece___init___py.html">src\gmail_cleanup\data\__init__.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t21">src\gmail_cleanup\data\database.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html#t21"><data value='DatabaseManager'>DatabaseManager</data></a></td>
                <td>113</td>
                <td>113</td>
                <td>0</td>
                <td class="right" data-ratio="0 113">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html">src\gmail_cleanup\data\database.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t14">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t14"><data value='FilterOperator'>FilterOperator</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t27">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t27"><data value='FilterField'>FilterField</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t40">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t40"><data value='CleanupAction'>CleanupAction</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t48">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t48"><data value='RunStatus'>RunStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t58">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t58"><data value='FilterCriterion'>FilterCriterion</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t86">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t86"><data value='FilterRule'>FilterRule</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t114">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t114"><data value='SafetySettings'>SafetySettings</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t140">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t140"><data value='CleanupProfile'>CleanupProfile</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t180">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t180"><data value='EmailMessage'>EmailMessage</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t235">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t235"><data value='CleanupCandidate'>CleanupCandidate</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t266">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html#t266"><data value='CleanupRun'>CleanupRun</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html">src\gmail_cleanup\data\models.py</a></td>
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>125</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="125 125">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4672292787daf277___init___py.html">src\gmail_cleanup\engine\__init__.py</a></td>
                <td class="name left"><a href="z_4672292787daf277___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4672292787daf277_cleanup_processor_py.html#t24">src\gmail_cleanup\engine\cleanup_processor.py</a></td>
                <td class="name left"><a href="z_4672292787daf277_cleanup_processor_py.html#t24"><data value='CleanupProcessor'>CleanupProcessor</data></a></td>
                <td>145</td>
                <td>145</td>
                <td>0</td>
                <td class="right" data-ratio="0 145">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4672292787daf277_cleanup_processor_py.html">src\gmail_cleanup\engine\cleanup_processor.py</a></td>
                <td class="name left"><a href="z_4672292787daf277_cleanup_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4672292787daf277_rule_engine_py.html#t21">src\gmail_cleanup\engine\rule_engine.py</a></td>
                <td class="name left"><a href="z_4672292787daf277_rule_engine_py.html#t21"><data value='RuleEngine'>RuleEngine</data></a></td>
                <td>228</td>
                <td>228</td>
                <td>0</td>
                <td class="right" data-ratio="0 228">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4672292787daf277_rule_engine_py.html">src\gmail_cleanup\engine\rule_engine.py</a></td>
                <td class="name left"><a href="z_4672292787daf277_rule_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3___init___py.html">src\gmail_cleanup\gui\__init__.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t30">src\gmail_cleanup\gui\main_window.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html#t30"><data value='MainWindow'>MainWindow</data></a></td>
                <td>87</td>
                <td>87</td>
                <td>0</td>
                <td class="right" data-ratio="0 87">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html">src\gmail_cleanup\gui\main_window.py</a></td>
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e___init___py.html">src\gmail_cleanup\gui\tabs\__init__.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_backups_reports_py.html#t19">src\gmail_cleanup\gui\tabs\backups_reports.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_backups_reports_py.html#t19"><data value='BackupsReportsTab'>BackupsReportsTab</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_backups_reports_py.html">src\gmail_cleanup\gui\tabs\backups_reports.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_backups_reports_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t19">src\gmail_cleanup\gui\tabs\base_tab.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html#t19"><data value='BaseTab'>BaseTab</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html">src\gmail_cleanup\gui\tabs\base_tab.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_cleanup_py.html#t19">src\gmail_cleanup\gui\tabs\cleanup.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_cleanup_py.html#t19"><data value='CleanupTab'>CleanupTab</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_cleanup_py.html">src\gmail_cleanup\gui\tabs\cleanup.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_cleanup_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_criteria_builder_py.html#t30">src\gmail_cleanup\gui\tabs\criteria_builder.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_criteria_builder_py.html#t30"><data value='CriterionWidget'>CriterionWidget</data></a></td>
                <td>100</td>
                <td>100</td>
                <td>0</td>
                <td class="right" data-ratio="0 100">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_criteria_builder_py.html#t260">src\gmail_cleanup\gui\tabs\criteria_builder.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_criteria_builder_py.html#t260"><data value='CriteriaBuilderTab'>CriteriaBuilderTab</data></a></td>
                <td>251</td>
                <td>251</td>
                <td>0</td>
                <td class="right" data-ratio="0 251">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_criteria_builder_py.html">src\gmail_cleanup\gui\tabs\criteria_builder.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_criteria_builder_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t23">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t23"><data value='StatCard'>StatCard</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t69">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html#t69"><data value='DashboardTab'>DashboardTab</data></a></td>
                <td>114</td>
                <td>114</td>
                <td>0</td>
                <td class="right" data-ratio="0 114">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_logs_py.html#t19">src\gmail_cleanup\gui\tabs\logs.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_logs_py.html#t19"><data value='LogsTab'>LogsTab</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_logs_py.html">src\gmail_cleanup\gui\tabs\logs.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_logs_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_preview_py.html#t31">src\gmail_cleanup\gui\tabs\preview.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_preview_py.html#t31"><data value='PreviewWorker'>PreviewWorker</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_preview_py.html#t61">src\gmail_cleanup\gui\tabs\preview.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_preview_py.html#t61"><data value='PreviewTab'>PreviewTab</data></a></td>
                <td>312</td>
                <td>312</td>
                <td>0</td>
                <td class="right" data-ratio="0 312">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_preview_py.html">src\gmail_cleanup\gui\tabs\preview.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_preview_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_settings_py.html#t25">src\gmail_cleanup\gui\tabs\settings.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_settings_py.html#t25"><data value='AuthenticationWorker'>AuthenticationWorker</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_settings_py.html#t48">src\gmail_cleanup\gui\tabs\settings.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_settings_py.html#t48"><data value='SettingsTab'>SettingsTab</data></a></td>
                <td>184</td>
                <td>184</td>
                <td>0</td>
                <td class="right" data-ratio="0 184">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_settings_py.html">src\gmail_cleanup\gui\tabs\settings.py</a></td>
                <td class="name left"><a href="z_699f37a4a3595e6e_settings_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f4fb5d423fccdd22_main_py.html">src\gmail_cleanup\main.py</a></td>
                <td class="name left"><a href="z_f4fb5d423fccdd22_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>2</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>2808</td>
                <td>2602</td>
                <td>2</td>
                <td class="right" data-ratio="206 2808">7%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-19 10:04 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
