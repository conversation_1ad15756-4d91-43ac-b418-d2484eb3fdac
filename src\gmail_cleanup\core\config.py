"""
Application configuration management.

Handles application settings, paths, and user preferences.
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class AppConfig:
    """Manages application configuration and paths."""
    
    def __init__(self, config_dir: Optional[Path] = None):
        """Initialize configuration with optional custom config directory."""
        self._config_dir = config_dir or self._get_default_config_dir()
        self._config_file = self._config_dir / "config.json"
        self._settings: Dict[str, Any] = {}
        
        # Ensure directories exist
        self._ensure_directories()
        
        # Load existing configuration
        self._load_config()
    
    def _get_default_config_dir(self) -> Path:
        """Get the default configuration directory based on OS."""
        if os.name == 'nt':  # Windows
            base = Path(os.environ.get('APPDATA', Path.home() / 'AppData' / 'Roaming'))
        elif os.name == 'posix':  # macOS/Linux
            if 'darwin' in os.uname().sysname.lower():  # macOS
                base = Path.home() / 'Library' / 'Application Support'
            else:  # Linux
                base = Path(os.environ.get('XDG_CONFIG_HOME', Path.home() / '.config'))
        else:
            base = Path.home()
        
        return base / "GmailCleanupTool"
    
    def _ensure_directories(self) -> None:
        """Ensure all required directories exist."""
        directories = [
            self._config_dir,
            self.get_data_dir(),
            self.get_logs_dir(),
            self.get_backups_dir(),
            self.get_exports_dir(),
            self.get_profiles_dir(),
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def _load_config(self) -> None:
        """Load configuration from file."""
        if self._config_file.exists():
            try:
                with open(self._config_file, 'r', encoding='utf-8') as f:
                    self._settings = json.load(f)
                logger.info(f"Loaded configuration from {self._config_file}")
            except Exception as e:
                logger.warning(f"Failed to load config file: {e}")
                self._settings = {}
        else:
            self._settings = self._get_default_settings()
            self.save_config()
    
    def _get_default_settings(self) -> Dict[str, Any]:
        """Get default application settings."""
        return {
            "version": "1.0.0",
            "oauth": {
                "client_id": "",
                "client_secret": "",
                "scopes": [
                    "https://www.googleapis.com/auth/gmail.readonly",
                    "https://www.googleapis.com/auth/gmail.modify"
                ]
            },
            "safety": {
                "skip_starred": True,
                "skip_important": True,
                "skip_from_contacts": True,
                "thread_mode": "message",  # "message" or "thread"
                "protected_terms": [
                    "invoice", "receipt", "statement", "tax", "insurance",
                    "order #", "shipping", "reservation", "boarding pass",
                    "verification code", "password reset", "calendar invitation",
                    "school", "daycare", "legal", "financial"
                ]
            },
            "quotas": {
                "batch_size": 100,
                "per_batch_delay_ms": 1000,
                "max_messages_per_run": 10000
            },
            "ui": {
                "theme": "system",  # "light", "dark", "system"
                "window_geometry": None,
                "window_state": None
            },
            "logging": {
                "level": "INFO",
                "max_log_files": 10,
                "max_log_size_mb": 10
            }
        }
    
    def save_config(self) -> None:
        """Save current configuration to file."""
        try:
            with open(self._config_file, 'w', encoding='utf-8') as f:
                json.dump(self._settings, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved configuration to {self._config_file}")
        except Exception as e:
            logger.error(f"Failed to save config file: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get a configuration value using dot notation."""
        keys = key.split('.')
        value = self._settings
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """Set a configuration value using dot notation."""
        keys = key.split('.')
        target = self._settings
        
        for k in keys[:-1]:
            if k not in target:
                target[k] = {}
            target = target[k]
        
        target[keys[-1]] = value
        self.save_config()
    
    # Directory getters
    def get_config_dir(self) -> Path:
        """Get the configuration directory."""
        return self._config_dir
    
    def get_data_dir(self) -> Path:
        """Get the data directory."""
        return self._config_dir / "data"
    
    def get_logs_dir(self) -> Path:
        """Get the logs directory."""
        return self._config_dir / "logs"
    
    def get_backups_dir(self) -> Path:
        """Get the backups directory."""
        return self._config_dir / "backups"
    
    def get_exports_dir(self) -> Path:
        """Get the exports directory."""
        return self._config_dir / "exports"
    
    def get_profiles_dir(self) -> Path:
        """Get the profiles directory."""
        return self._config_dir / "profiles"
    
    def get_database_path(self) -> Path:
        """Get the SQLite database path."""
        return self.get_data_dir() / "gmail_cleanup.db"
