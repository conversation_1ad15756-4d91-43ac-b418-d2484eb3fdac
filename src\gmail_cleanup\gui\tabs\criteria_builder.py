"""
Criteria Builder tab for the Gmail Cleanup Tool.

Provides visual rule builder interface for configuring cleanup criteria.
"""

import logging
from typing import Optional, List

from PySide6.QtWidgets import (
    QLabel, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QComboBox,
    QLineEdit, QPushButton, QScrollArea, QFrame, QFormLayout,
    QSpinBox, QDoubleSpinBox, QCheckBox, QTextEdit, QMessageBox,
    QFileDialog, QListWidget, QListWidgetItem
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from .base_tab import BaseTab
from ...core.config import AppConfig
from ...data.models import (
    FilterCriterion, FilterRule, SafetySettings, CleanupProfile,
    FilterField, FilterOperator, CleanupAction
)
from ...data.database import DatabaseManager

logger = logging.getLogger(__name__)


class CriterionWidget(QFrame):
    """Widget for editing a single filter criterion."""

    remove_requested = Signal(object)  # Emits self when remove is requested

    def __init__(self, criterion: Optional[FilterCriterion] = None, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 5px;
                margin: 2px;
                background-color: #f9f9f9;
            }
        """)

        self.criterion = criterion or FilterCriterion(
            field=FilterField.SENDER,
            operator=FilterOperator.CONTAINS,
            value=""
        )

        self._setup_ui()
        self._load_criterion()

    def _setup_ui(self) -> None:
        """Set up the criterion widget UI."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)

        # Field selector
        self.field_combo = QComboBox()
        self.field_combo.addItems([
            "Sender", "Subject", "Date", "Size", "Label", "Category", "Header", "Status"
        ])
        self.field_combo.currentTextChanged.connect(self._on_field_changed)
        layout.addWidget(self.field_combo)

        # Operator selector
        self.operator_combo = QComboBox()
        self.operator_combo.currentTextChanged.connect(self._on_operator_changed)
        layout.addWidget(self.operator_combo)

        # Value input (will be replaced based on field type)
        self.value_widget = QLineEdit()
        layout.addWidget(self.value_widget)

        # Case sensitive checkbox
        self.case_sensitive_cb = QCheckBox("Case sensitive")
        layout.addWidget(self.case_sensitive_cb)

        # Remove button
        remove_btn = QPushButton("✖")
        remove_btn.setMaximumWidth(30)
        remove_btn.setStyleSheet("QPushButton { color: red; font-weight: bold; }")
        remove_btn.clicked.connect(lambda: self.remove_requested.emit(self))
        layout.addWidget(remove_btn)

    def _on_field_changed(self) -> None:
        """Handle field selection change."""
        field_text = self.field_combo.currentText()

        # Update operators based on field
        self.operator_combo.clear()

        if field_text in ["Sender", "Subject"]:
            self.operator_combo.addItems([
                "Contains", "Equals", "Starts with", "Ends with", "Regex"
            ])
        elif field_text == "Date":
            self.operator_combo.addItems(["Older than", "Newer than"])
        elif field_text == "Size":
            self.operator_combo.addItems(["Larger than", "Smaller than"])
        elif field_text in ["Label", "Category"]:
            self.operator_combo.addItems(["Equals", "In list", "Not in list"])
        elif field_text == "Header":
            self.operator_combo.addItems(["Contains", "Equals"])
        elif field_text == "Status":
            self.operator_combo.addItems(["Equals"])

        self._update_value_widget()

    def _on_operator_changed(self) -> None:
        """Handle operator selection change."""
        self._update_value_widget()

    def _update_value_widget(self) -> None:
        """Update value widget based on field and operator."""
        field_text = self.field_combo.currentText()
        operator_text = self.operator_combo.currentText()

        # Remove current value widget
        if self.value_widget:
            self.value_widget.setParent(None)

        # Create appropriate widget
        if field_text == "Date" and operator_text in ["Older than", "Newer than"]:
            self.value_widget = QComboBox()
            self.value_widget.addItems([
                "7d", "30d", "90d", "180d", "1y", "2y", "Custom..."
            ])
        elif field_text == "Size":
            self.value_widget = QDoubleSpinBox()
            self.value_widget.setRange(0.1, 1000.0)
            self.value_widget.setSuffix(" MB")
            self.value_widget.setValue(10.0)
        elif field_text == "Status":
            self.value_widget = QComboBox()
            self.value_widget.addItems(["unread", "read", "starred", "important"])
        elif field_text == "Category":
            self.value_widget = QComboBox()
            self.value_widget.addItems([
                "promotions", "social", "updates", "forums", "primary"
            ])
        else:
            self.value_widget = QLineEdit()
            if field_text == "Sender":
                self.value_widget.setPlaceholderText("e.g., @example.com or noreply@")
            elif field_text == "Subject":
                self.value_widget.setPlaceholderText("e.g., unsubscribe or sale")
            elif field_text == "Header":
                self.value_widget.setPlaceholderText("e.g., list-unsubscribe:exists")
            elif field_text == "Label":
                self.value_widget.setPlaceholderText("e.g., INBOX or SPAM")

        # Insert widget back into layout
        layout = self.layout()
        layout.insertWidget(2, self.value_widget)

    def _load_criterion(self) -> None:
        """Load criterion data into widgets."""
        # Set field
        field_map = {
            FilterField.SENDER: "Sender",
            FilterField.SUBJECT: "Subject",
            FilterField.DATE: "Date",
            FilterField.SIZE: "Size",
            FilterField.LABEL: "Label",
            FilterField.CATEGORY: "Category",
            FilterField.HEADER: "Header",
            FilterField.STATUS: "Status"
        }
        field_text = field_map.get(self.criterion.field, "Sender")
        self.field_combo.setCurrentText(field_text)

        # Set operator
        operator_map = {
            FilterOperator.CONTAINS: "Contains",
            FilterOperator.EQUALS: "Equals",
            FilterOperator.STARTS_WITH: "Starts with",
            FilterOperator.ENDS_WITH: "Ends with",
            FilterOperator.REGEX: "Regex",
            FilterOperator.GREATER_THAN: "Larger than" if self.criterion.field == FilterField.SIZE else "Older than",
            FilterOperator.LESS_THAN: "Smaller than" if self.criterion.field == FilterField.SIZE else "Newer than",
            FilterOperator.IN_LIST: "In list",
            FilterOperator.NOT_IN_LIST: "Not in list"
        }
        operator_text = operator_map.get(self.criterion.operator, "Contains")
        self.operator_combo.setCurrentText(operator_text)

        # Set value
        if hasattr(self.value_widget, 'setText'):
            self.value_widget.setText(str(self.criterion.value))
        elif hasattr(self.value_widget, 'setCurrentText'):
            self.value_widget.setCurrentText(str(self.criterion.value))
        elif hasattr(self.value_widget, 'setValue'):
            try:
                self.value_widget.setValue(float(self.criterion.value))
            except (ValueError, TypeError):
                self.value_widget.setValue(10.0)

        # Set case sensitive
        self.case_sensitive_cb.setChecked(self.criterion.case_sensitive)

    def get_criterion(self) -> FilterCriterion:
        """Get the current criterion from the widget."""
        # Map field
        field_map = {
            "Sender": FilterField.SENDER,
            "Subject": FilterField.SUBJECT,
            "Date": FilterField.DATE,
            "Size": FilterField.SIZE,
            "Label": FilterField.LABEL,
            "Category": FilterField.CATEGORY,
            "Header": FilterField.HEADER,
            "Status": FilterField.STATUS
        }
        field = field_map[self.field_combo.currentText()]

        # Map operator
        operator_text = self.operator_combo.currentText()
        operator_map = {
            "Contains": FilterOperator.CONTAINS,
            "Equals": FilterOperator.EQUALS,
            "Starts with": FilterOperator.STARTS_WITH,
            "Ends with": FilterOperator.ENDS_WITH,
            "Regex": FilterOperator.REGEX,
            "Larger than": FilterOperator.GREATER_THAN,
            "Smaller than": FilterOperator.LESS_THAN,
            "Older than": FilterOperator.GREATER_THAN,
            "Newer than": FilterOperator.LESS_THAN,
            "In list": FilterOperator.IN_LIST,
            "Not in list": FilterOperator.NOT_IN_LIST
        }
        operator = operator_map[operator_text]

        # Get value
        if hasattr(self.value_widget, 'text'):
            value = self.value_widget.text()
        elif hasattr(self.value_widget, 'currentText'):
            value = self.value_widget.currentText()
        elif hasattr(self.value_widget, 'value'):
            if field == FilterField.SIZE:
                # Convert MB to bytes
                value = int(self.value_widget.value() * 1024 * 1024)
            else:
                value = self.value_widget.value()
        else:
            value = ""

        return FilterCriterion(
            field=field,
            operator=operator,
            value=value,
            case_sensitive=self.case_sensitive_cb.isChecked()
        )


class CriteriaBuilderTab(BaseTab):
    """Criteria builder tab for configuring cleanup rules."""

    def __init__(self, config: AppConfig, parent: Optional[QWidget] = None):
        # Initialize attributes first
        self.database = DatabaseManager(config)
        self.current_profile: Optional[CleanupProfile] = None
        self.criterion_widgets: List[CriterionWidget] = []

        # Then call parent constructor
        super().__init__(config, parent)

    def _setup_ui(self) -> None:
        """Set up the criteria builder UI."""
        # Title
        title = QLabel("Criteria Builder")
        title.setFont(QFont("Arial", 20, QFont.Bold))
        title.setStyleSheet("color: #333; margin-bottom: 10px;")
        self.layout.addWidget(title)

        # Profile management section
        self._create_profile_section()

        # Rule builder section
        self._create_rule_section()

        # Safety settings section
        self._create_safety_section()

        # Action settings section
        self._create_action_section()

        # Control buttons
        self._create_control_buttons()

        self.layout.addStretch()

    def _create_profile_section(self) -> None:
        """Create profile management section."""
        group = QGroupBox("Cleanup Profile")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QFormLayout(group)

        # Profile selector
        profile_layout = QHBoxLayout()
        self.profile_combo = QComboBox()
        self.profile_combo.currentTextChanged.connect(self._on_profile_selected)
        profile_layout.addWidget(self.profile_combo)

        new_btn = QPushButton("New")
        new_btn.clicked.connect(self._new_profile)
        profile_layout.addWidget(new_btn)

        save_btn = QPushButton("Save")
        save_btn.clicked.connect(self._save_profile)
        profile_layout.addWidget(save_btn)

        delete_btn = QPushButton("Delete")
        delete_btn.clicked.connect(self._delete_profile)
        profile_layout.addWidget(delete_btn)

        layout.addRow("Profile:", profile_layout)

        # Profile name
        self.profile_name_edit = QLineEdit()
        self.profile_name_edit.setPlaceholderText("Enter profile name")
        layout.addRow("Name:", self.profile_name_edit)

        # Profile description
        self.profile_desc_edit = QTextEdit()
        self.profile_desc_edit.setMaximumHeight(60)
        self.profile_desc_edit.setPlaceholderText("Enter profile description")
        layout.addRow("Description:", self.profile_desc_edit)

        self.layout.addWidget(group)

        # Load existing profiles
        self._refresh_profiles()

    def _create_rule_section(self) -> None:
        """Create rule builder section."""
        group = QGroupBox("Filter Rules")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QVBoxLayout(group)

        # Rule logic selector
        logic_layout = QHBoxLayout()
        logic_layout.addWidget(QLabel("Logic:"))
        self.logic_combo = QComboBox()
        self.logic_combo.addItems(["AND (all rules must match)", "OR (any rule can match)"])
        logic_layout.addWidget(self.logic_combo)
        logic_layout.addStretch()
        layout.addLayout(logic_layout)

        # Criteria scroll area
        self.criteria_scroll = QScrollArea()
        self.criteria_scroll.setWidgetResizable(True)
        self.criteria_scroll.setMinimumHeight(200)

        self.criteria_widget = QWidget()
        self.criteria_layout = QVBoxLayout(self.criteria_widget)
        self.criteria_layout.addStretch()

        self.criteria_scroll.setWidget(self.criteria_widget)
        layout.addWidget(self.criteria_scroll)

        # Add criterion button
        add_btn = QPushButton("+ Add Filter Criterion")
        add_btn.clicked.connect(self._add_criterion)
        layout.addWidget(add_btn)

        self.layout.addWidget(group)

    def _create_safety_section(self) -> None:
        """Create safety settings section."""
        group = QGroupBox("Safety Settings")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QVBoxLayout(group)

        # Skip options
        self.skip_starred_cb = QCheckBox("Skip starred messages")
        self.skip_starred_cb.setChecked(True)
        layout.addWidget(self.skip_starred_cb)

        self.skip_important_cb = QCheckBox("Skip important messages")
        self.skip_important_cb.setChecked(True)
        layout.addWidget(self.skip_important_cb)

        self.skip_contacts_cb = QCheckBox("Skip messages from contacts")
        self.skip_contacts_cb.setChecked(True)
        layout.addWidget(self.skip_contacts_cb)

        # Thread mode
        thread_layout = QHBoxLayout()
        thread_layout.addWidget(QLabel("Thread processing:"))
        self.thread_mode_combo = QComboBox()
        self.thread_mode_combo.addItems(["Individual messages", "Entire threads"])
        thread_layout.addWidget(self.thread_mode_combo)
        thread_layout.addStretch()
        layout.addLayout(thread_layout)

        self.layout.addWidget(group)

    def _create_action_section(self) -> None:
        """Create action settings section."""
        group = QGroupBox("Cleanup Action")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QFormLayout(group)

        # Action selector
        self.action_combo = QComboBox()
        self.action_combo.addItems([
            "Move to Trash (recommended)",
            "Archive (remove from Inbox)",
            "Label only (for testing)",
            "Permanent Delete (dangerous!)"
        ])
        layout.addRow("Action:", self.action_combo)

        # Size filter
        size_layout = QHBoxLayout()
        self.size_filter_cb = QCheckBox("Only process messages larger than:")
        size_layout.addWidget(self.size_filter_cb)

        self.size_filter_spin = QDoubleSpinBox()
        self.size_filter_spin.setRange(0.1, 100.0)
        self.size_filter_spin.setValue(5.0)
        self.size_filter_spin.setSuffix(" MB")
        self.size_filter_spin.setEnabled(False)
        size_layout.addWidget(self.size_filter_spin)

        self.size_filter_cb.toggled.connect(self.size_filter_spin.setEnabled)
        size_layout.addStretch()

        layout.addRow("Size filter:", size_layout)

        self.layout.addWidget(group)

    def _create_control_buttons(self) -> None:
        """Create control buttons."""
        button_layout = QHBoxLayout()

        # Test rule button
        test_btn = QPushButton("🔍 Test Rules")
        test_btn.clicked.connect(self._test_rules)
        button_layout.addWidget(test_btn)

        # Import/Export buttons
        import_btn = QPushButton("📁 Import Profile")
        import_btn.clicked.connect(self._import_profile)
        button_layout.addWidget(import_btn)

        export_btn = QPushButton("💾 Export Profile")
        export_btn.clicked.connect(self._export_profile)
        button_layout.addWidget(export_btn)

        button_layout.addStretch()

        # Clear button
        clear_btn = QPushButton("🗑️ Clear All")
        clear_btn.clicked.connect(self._clear_all)
        button_layout.addWidget(clear_btn)

        self.layout.addLayout(button_layout)

    def _refresh_profiles(self) -> None:
        """Refresh the profile list."""
        self.profile_combo.clear()
        self.profile_combo.addItem("-- Select Profile --")

        profiles = self.database.list_profiles()
        for profile in profiles:
            self.profile_combo.addItem(profile.name)

    def _on_profile_selected(self, profile_name: str) -> None:
        """Handle profile selection."""
        # Skip if UI not fully initialized
        if not hasattr(self, 'skip_starred_cb'):
            return

        if profile_name == "-- Select Profile --":
            self._clear_all()
            return

        profile = self.database.load_profile(profile_name)
        if profile:
            self.current_profile = profile
            self._load_profile(profile)

    def _load_profile(self, profile: CleanupProfile) -> None:
        """Load profile data into the UI."""
        # Load basic info
        self.profile_name_edit.setText(profile.name)
        self.profile_desc_edit.setPlainText(profile.description)

        # Load safety settings
        self.skip_starred_cb.setChecked(profile.safety_settings.skip_starred)
        self.skip_important_cb.setChecked(profile.safety_settings.skip_important)
        self.skip_contacts_cb.setChecked(profile.safety_settings.skip_from_contacts)

        thread_index = 0 if profile.safety_settings.thread_mode == "message" else 1
        self.thread_mode_combo.setCurrentIndex(thread_index)

        # Load action
        action_map = {
            CleanupAction.TRASH: 0,
            CleanupAction.ARCHIVE: 1,
            CleanupAction.LABEL_ONLY: 2,
            CleanupAction.DELETE_PERMANENT: 3
        }
        self.action_combo.setCurrentIndex(action_map.get(profile.action, 0))

        # Load size filter
        if profile.size_filter_mb:
            self.size_filter_cb.setChecked(True)
            self.size_filter_spin.setValue(profile.size_filter_mb)
        else:
            self.size_filter_cb.setChecked(False)

        # Load rules
        self._clear_criteria()
        if profile.rules:
            rule = profile.rules[0]  # Use first rule for now
            logic_index = 0 if rule.logic == "AND" else 1
            self.logic_combo.setCurrentIndex(logic_index)

            for criterion in rule.criteria:
                self._add_criterion(criterion)

    def _add_criterion(self, criterion: Optional[FilterCriterion] = None) -> None:
        """Add a new criterion widget."""
        widget = CriterionWidget(criterion)
        widget.remove_requested.connect(self._remove_criterion)

        # Insert before the stretch
        self.criteria_layout.insertWidget(len(self.criterion_widgets), widget)
        self.criterion_widgets.append(widget)

    def _remove_criterion(self, widget: CriterionWidget) -> None:
        """Remove a criterion widget."""
        if widget in self.criterion_widgets:
            self.criterion_widgets.remove(widget)
            widget.setParent(None)

    def _clear_criteria(self) -> None:
        """Clear all criteria widgets."""
        for widget in self.criterion_widgets:
            widget.setParent(None)
        self.criterion_widgets.clear()

    def _get_current_profile(self) -> CleanupProfile:
        """Get the current profile from the UI."""
        # Get basic info
        name = self.profile_name_edit.text().strip()
        description = self.profile_desc_edit.toPlainText().strip()

        # Get safety settings
        safety_settings = SafetySettings(
            skip_starred=self.skip_starred_cb.isChecked(),
            skip_important=self.skip_important_cb.isChecked(),
            skip_from_contacts=self.skip_contacts_cb.isChecked(),
            thread_mode="message" if self.thread_mode_combo.currentIndex() == 0 else "thread"
        )

        # Get action
        action_map = {
            0: CleanupAction.TRASH,
            1: CleanupAction.ARCHIVE,
            2: CleanupAction.LABEL_ONLY,
            3: CleanupAction.DELETE_PERMANENT
        }
        action = action_map[self.action_combo.currentIndex()]

        # Get size filter
        size_filter_mb = None
        if self.size_filter_cb.isChecked():
            size_filter_mb = self.size_filter_spin.value()

        # Get criteria
        criteria = []
        for widget in self.criterion_widgets:
            criteria.append(widget.get_criterion())

        # Create rule
        logic = "AND" if self.logic_combo.currentIndex() == 0 else "OR"
        rule = FilterRule(
            criteria=criteria,
            logic=logic,
            name=name,
            description=description
        )

        # Create profile
        profile = CleanupProfile(
            name=name,
            description=description,
            rules=[rule] if criteria else [],
            safety_settings=safety_settings,
            action=action,
            size_filter_mb=size_filter_mb
        )

        return profile

    def _new_profile(self) -> None:
        """Create a new profile."""
        self.current_profile = None
        self.profile_combo.setCurrentIndex(0)
        self._clear_all()
        self.profile_name_edit.setFocus()

    def _save_profile(self) -> None:
        """Save the current profile."""
        try:
            profile = self._get_current_profile()

            if not profile.name:
                self.emit_warning("Missing Name", "Please enter a profile name.")
                return

            if not profile.rules or not profile.rules[0].criteria:
                self.emit_warning("No Rules", "Please add at least one filter criterion.")
                return

            # Save to database
            if self.database.save_profile(profile):
                self.current_profile = profile
                self.emit_info("Profile Saved", f"Profile '{profile.name}' saved successfully.")
                self._refresh_profiles()

                # Select the saved profile
                index = self.profile_combo.findText(profile.name)
                if index >= 0:
                    self.profile_combo.setCurrentIndex(index)
            else:
                self.emit_error("Save Failed", "Failed to save profile to database.")

        except Exception as e:
            self.emit_error("Save Error", f"Error saving profile: {e}")

    def _delete_profile(self) -> None:
        """Delete the current profile."""
        if not self.current_profile:
            self.emit_warning("No Profile", "No profile selected to delete.")
            return

        reply = QMessageBox.question(
            self,
            "Delete Profile",
            f"Are you sure you want to delete profile '{self.current_profile.name}'?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            if self.database.delete_profile(self.current_profile.name):
                self.emit_info("Profile Deleted", f"Profile '{self.current_profile.name}' deleted.")
                self._refresh_profiles()
                self._new_profile()
            else:
                self.emit_error("Delete Failed", "Failed to delete profile.")

    def _clear_all(self) -> None:
        """Clear all form data."""
        self.profile_name_edit.clear()
        self.profile_desc_edit.clear()
        self._clear_criteria()

        # Reset to defaults
        self.skip_starred_cb.setChecked(True)
        self.skip_important_cb.setChecked(True)
        self.skip_contacts_cb.setChecked(True)
        self.thread_mode_combo.setCurrentIndex(0)
        self.action_combo.setCurrentIndex(0)
        self.size_filter_cb.setChecked(False)
        self.logic_combo.setCurrentIndex(0)

    def _test_rules(self) -> None:
        """Test the current rules."""
        if not self.require_authentication():
            return

        try:
            profile = self._get_current_profile()

            if not profile.rules or not profile.rules[0].criteria:
                self.emit_warning("No Rules", "Please add at least one filter criterion to test.")
                return

            # TODO: Implement rule testing with actual Gmail data
            self.emit_info("Test Rules", "Rule testing will be implemented in the preview tab.")

        except Exception as e:
            self.emit_error("Test Error", f"Error testing rules: {e}")

    def _import_profile(self) -> None:
        """Import profile from file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Import Profile",
            "",
            "JSON Files (*.json);;YAML Files (*.yaml *.yml);;All Files (*)"
        )

        if file_path:
            try:
                import json
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                profile = CleanupProfile.from_dict(data)
                self._load_profile(profile)
                self.emit_info("Import Successful", f"Profile '{profile.name}' imported successfully.")

            except Exception as e:
                self.emit_error("Import Failed", f"Failed to import profile: {e}")

    def _export_profile(self) -> None:
        """Export current profile to file."""
        try:
            profile = self._get_current_profile()

            if not profile.name:
                self.emit_warning("Missing Name", "Please enter a profile name before exporting.")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Export Profile",
                f"{profile.name}.json",
                "JSON Files (*.json);;All Files (*)"
            )

            if file_path:
                import json
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(profile.to_dict(), f, indent=2, ensure_ascii=False)

                self.emit_info("Export Successful", f"Profile exported to {file_path}")

        except Exception as e:
            self.emit_error("Export Failed", f"Failed to export profile: {e}")
