"""
OAuth 2.0 authentication manager for Gmail API.

Handles the complete OAuth flow including token storage and refresh.
"""

import json
import logging
import webbrowser
from pathlib import Path
from typing import Optional, List, Dict, Any
from urllib.parse import urlparse, parse_qs
import socket
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler
import time

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from .token_storage import TokenStorage
from ..core.config import AppConfig
from ..core.logging_config import log_api_call

logger = logging.getLogger(__name__)


class OAuthCallbackHandler(BaseHTTPRequestHandler):
    """HTTP handler for OAuth callback."""
    
    def do_GET(self):
        """Handle GET request for OAuth callback."""
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        
        # Parse the authorization code from the URL
        parsed_url = urlparse(self.path)
        query_params = parse_qs(parsed_url.query)
        
        if 'code' in query_params:
            self.server.auth_code = query_params['code'][0]
            response_html = """
            <html>
            <head><title>Gmail Cleanup Tool - Authentication</title></head>
            <body>
                <h2>Authentication Successful!</h2>
                <p>You can now close this window and return to the Gmail Cleanup Tool.</p>
                <script>window.close();</script>
            </body>
            </html>
            """
        elif 'error' in query_params:
            self.server.auth_error = query_params['error'][0]
            response_html = """
            <html>
            <head><title>Gmail Cleanup Tool - Authentication Error</title></head>
            <body>
                <h2>Authentication Failed</h2>
                <p>Error: {}</p>
                <p>Please close this window and try again.</p>
            </body>
            </html>
            """.format(query_params['error'][0])
        else:
            response_html = """
            <html>
            <head><title>Gmail Cleanup Tool - Authentication</title></head>
            <body>
                <h2>Unknown Response</h2>
                <p>Please close this window and try again.</p>
            </body>
            </html>
            """
        
        self.wfile.write(response_html.encode())
    
    def log_message(self, format, *args):
        """Suppress default HTTP server logging."""
        pass


class OAuthManager:
    """Manages OAuth 2.0 authentication for Gmail API."""
    
    # OAuth scopes
    READONLY_SCOPE = "https://www.googleapis.com/auth/gmail.readonly"
    MODIFY_SCOPE = "https://www.googleapis.com/auth/gmail.modify"
    
    def __init__(self, config: AppConfig):
        self.config = config
        self.token_storage = TokenStorage(config)
        self._credentials: Optional[Credentials] = None
        self._gmail_service = None
        
        # Load existing credentials
        self._load_credentials()
    
    def _load_credentials(self) -> None:
        """Load credentials from storage."""
        try:
            creds_data = self.token_storage.load_credentials()
            if creds_data:
                self._credentials = Credentials.from_authorized_user_info(creds_data)
                logger.info("Loaded existing credentials")
        except Exception as e:
            logger.warning(f"Failed to load credentials: {e}")
            self._credentials = None
    
    def _save_credentials(self) -> None:
        """Save credentials to storage."""
        if self._credentials:
            try:
                creds_data = {
                    'token': self._credentials.token,
                    'refresh_token': self._credentials.refresh_token,
                    'token_uri': self._credentials.token_uri,
                    'client_id': self._credentials.client_id,
                    'client_secret': self._credentials.client_secret,
                    'scopes': self._credentials.scopes
                }
                self.token_storage.save_credentials(creds_data)
                logger.info("Saved credentials")
            except Exception as e:
                logger.error(f"Failed to save credentials: {e}")
    
    def is_authenticated(self) -> bool:
        """Check if user is currently authenticated."""
        if not self._credentials:
            return False
        
        if not self._credentials.valid:
            if self._credentials.expired and self._credentials.refresh_token:
                try:
                    self._credentials.refresh(Request())
                    self._save_credentials()
                    logger.info("Refreshed expired credentials")
                except Exception as e:
                    logger.error(f"Failed to refresh credentials: {e}")
                    return False
            else:
                return False
        
        return True
    
    def get_required_scopes(self, include_delete: bool = False) -> List[str]:
        """Get the required OAuth scopes."""
        scopes = [self.READONLY_SCOPE, self.MODIFY_SCOPE]
        if include_delete:
            # Note: Gmail API doesn't have a separate delete scope
            # Delete is included in modify scope
            pass
        return scopes
    
    def authenticate(self, client_id: str, client_secret: str, include_delete: bool = False) -> bool:
        """
        Perform OAuth authentication flow.
        
        Args:
            client_id: OAuth client ID
            client_secret: OAuth client secret
            include_delete: Whether to request delete permissions
            
        Returns:
            True if authentication successful, False otherwise
        """
        try:
            # Create OAuth flow
            scopes = self.get_required_scopes(include_delete)
            
            # Create client config
            client_config = {
                "web": {
                    "client_id": client_id,
                    "client_secret": client_secret,
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "redirect_uris": ["http://localhost:8080"]
                }
            }
            
            flow = Flow.from_client_config(
                client_config,
                scopes=scopes,
                redirect_uri="http://localhost:8080"
            )
            
            # Start local server for callback
            server = HTTPServer(('localhost', 8080), OAuthCallbackHandler)
            server.auth_code = None
            server.auth_error = None
            
            # Start server in background thread
            server_thread = threading.Thread(target=server.serve_forever)
            server_thread.daemon = True
            server_thread.start()
            
            try:
                # Get authorization URL
                auth_url, _ = flow.authorization_url(
                    access_type='offline',
                    include_granted_scopes='true',
                    prompt='consent'  # Force consent to get refresh token
                )
                
                logger.info(f"Opening authorization URL: {auth_url}")
                
                # Open browser
                webbrowser.open(auth_url)
                
                # Wait for callback
                logger.info("Waiting for OAuth callback...")
                timeout = 300  # 5 minutes
                for _ in range(timeout):
                    if server.auth_code or server.auth_error:
                        break
                    time.sleep(1)
                else:
                    logger.error("OAuth callback timeout")
                    return False
                
                if server.auth_error:
                    logger.error(f"OAuth error: {server.auth_error}")
                    return False
                
                if not server.auth_code:
                    logger.error("No authorization code received")
                    return False
                
                # Exchange code for tokens
                flow.fetch_token(code=server.auth_code)
                self._credentials = flow.credentials
                
                # Save credentials
                self._save_credentials()
                
                # Save client credentials to config
                self.config.set("oauth.client_id", client_id)
                self.config.set("oauth.client_secret", client_secret)
                self.config.set("oauth.scopes", scopes)
                
                logger.info("OAuth authentication successful")
                log_api_call("OAuth", "authenticate", "success")
                
                return True
                
            finally:
                server.shutdown()
                server.server_close()
                
        except Exception as e:
            logger.error(f"OAuth authentication failed: {e}")
            log_api_call("OAuth", "authenticate", "error", {"error": str(e)})
            return False
    
    def revoke_authentication(self) -> bool:
        """Revoke current authentication."""
        try:
            if self._credentials and self._credentials.token:
                # Revoke token
                revoke_url = f"https://oauth2.googleapis.com/revoke?token={self._credentials.token}"
                import urllib.request
                urllib.request.urlopen(revoke_url)
                
            # Clear stored credentials
            self.token_storage.clear_credentials()
            self._credentials = None
            self._gmail_service = None
            
            logger.info("Authentication revoked")
            log_api_call("OAuth", "revoke", "success")
            return True
            
        except Exception as e:
            logger.error(f"Failed to revoke authentication: {e}")
            log_api_call("OAuth", "revoke", "error", {"error": str(e)})
            return False
    
    def get_gmail_service(self):
        """Get authenticated Gmail service instance."""
        if not self.is_authenticated():
            raise ValueError("Not authenticated")
        
        if not self._gmail_service:
            self._gmail_service = build('gmail', 'v1', credentials=self._credentials)
            logger.debug("Created Gmail service instance")
        
        return self._gmail_service
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """Get authenticated user information."""
        try:
            if not self.is_authenticated():
                return None
            
            service = self.get_gmail_service()
            profile = service.users().getProfile(userId='me').execute()
            
            log_api_call("Gmail", "users.getProfile", "success")
            return {
                'email': profile.get('emailAddress'),
                'messages_total': profile.get('messagesTotal', 0),
                'threads_total': profile.get('threadsTotal', 0),
                'history_id': profile.get('historyId')
            }
            
        except HttpError as e:
            logger.error(f"Failed to get user info: {e}")
            log_api_call("Gmail", "users.getProfile", "error", {"error": str(e)})
            return None
        except Exception as e:
            logger.error(f"Unexpected error getting user info: {e}")
            return None
    
    def test_connection(self) -> bool:
        """Test the Gmail API connection."""
        try:
            user_info = self.get_user_info()
            return user_info is not None
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False
