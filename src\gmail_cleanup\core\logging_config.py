"""
Logging configuration for the Gmail Cleanup Tool.

Sets up both structured logging for machine processing and human-readable logs.
"""

import logging
import logging.handlers
import json
from pathlib import Path
from typing import Dict, Any
from datetime import datetime


class JSONFormatter(logging.Formatter):
    """Custom formatter for structured JSON logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON."""
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields if present
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        return json.dumps(log_entry, ensure_ascii=False)


class HumanFormatter(logging.Formatter):
    """Human-readable formatter for activity logs."""
    
    def __init__(self):
        super().__init__(
            fmt='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )


def setup_logging(logs_dir: Path, level: str = "INFO") -> None:
    """Set up logging configuration."""
    logs_dir.mkdir(parents=True, exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, level.upper()))
    
    # Clear any existing handlers
    root_logger.handlers.clear()
    
    # JSON structured log file
    json_log_file = logs_dir / "gmail_cleanup.jsonl"
    json_handler = logging.handlers.RotatingFileHandler(
        json_log_file,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=10,
        encoding='utf-8'
    )
    json_handler.setFormatter(JSONFormatter())
    json_handler.setLevel(logging.DEBUG)
    root_logger.addHandler(json_handler)
    
    # Human-readable log file
    human_log_file = logs_dir / "gmail_cleanup.log"
    human_handler = logging.handlers.RotatingFileHandler(
        human_log_file,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=10,
        encoding='utf-8'
    )
    human_handler.setFormatter(HumanFormatter())
    human_handler.setLevel(logging.INFO)
    root_logger.addHandler(human_handler)
    
    # Console handler for development
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(HumanFormatter())
    console_handler.setLevel(logging.WARNING)
    root_logger.addHandler(console_handler)
    
    # Set specific logger levels
    logging.getLogger('googleapiclient').setLevel(logging.WARNING)
    logging.getLogger('google.auth').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)


def get_activity_logger(name: str) -> logging.Logger:
    """Get a logger for user activity tracking."""
    logger = logging.getLogger(f"activity.{name}")
    return logger


def log_user_action(action: str, details: Dict[str, Any] = None) -> None:
    """Log a user action with structured data."""
    logger = get_activity_logger("user")
    extra_fields = {
        "action": action,
        "details": details or {},
        "category": "user_action"
    }
    
    # Create a custom log record with extra fields
    record = logger.makeRecord(
        logger.name, logging.INFO, __file__, 0,
        f"User action: {action}", (), None
    )
    record.extra_fields = extra_fields
    logger.handle(record)


def log_api_call(method: str, endpoint: str, status: str, details: Dict[str, Any] = None) -> None:
    """Log an API call with structured data."""
    logger = get_activity_logger("api")
    extra_fields = {
        "method": method,
        "endpoint": endpoint,
        "status": status,
        "details": details or {},
        "category": "api_call"
    }
    
    record = logger.makeRecord(
        logger.name, logging.INFO, __file__, 0,
        f"API call: {method} {endpoint} - {status}", (), None
    )
    record.extra_fields = extra_fields
    logger.handle(record)
