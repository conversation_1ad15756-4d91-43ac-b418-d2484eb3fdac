LICENSE
README.md
pyproject.toml
src/gmail_cleanup/__init__.py
src/gmail_cleanup/main.py
src/gmail_cleanup/auth/__init__.py
src/gmail_cleanup/auth/oauth_manager.py
src/gmail_cleanup/auth/token_storage.py
src/gmail_cleanup/core/__init__.py
src/gmail_cleanup/core/config.py
src/gmail_cleanup/core/logging_config.py
src/gmail_cleanup/data/__init__.py
src/gmail_cleanup/data/database.py
src/gmail_cleanup/data/models.py
src/gmail_cleanup/gui/__init__.py
src/gmail_cleanup/gui/main_window.py
src/gmail_cleanup/gui/tabs/__init__.py
src/gmail_cleanup/gui/tabs/backups_reports.py
src/gmail_cleanup/gui/tabs/base_tab.py
src/gmail_cleanup/gui/tabs/cleanup.py
src/gmail_cleanup/gui/tabs/criteria_builder.py
src/gmail_cleanup/gui/tabs/dashboard.py
src/gmail_cleanup/gui/tabs/logs.py
src/gmail_cleanup/gui/tabs/preview.py
src/gmail_cleanup/gui/tabs/settings.py
src/gmail_cleanup_tool.egg-info/PKG-INFO
src/gmail_cleanup_tool.egg-info/SOURCES.txt
src/gmail_cleanup_tool.egg-info/dependency_links.txt
src/gmail_cleanup_tool.egg-info/entry_points.txt
src/gmail_cleanup_tool.egg-info/requires.txt
src/gmail_cleanup_tool.egg-info/top_level.txt
tests/test_config.py
tests/test_models.py