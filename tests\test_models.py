"""
Tests for data models.
"""

import pytest
from datetime import datetime

from gmail_cleanup.data.models import (
    FilterCriterion, FilterRule, SafetySettings, CleanupProfile,
    EmailMessage, CleanupCandidate, CleanupRun,
    FilterField, FilterOperator, CleanupAction, RunStatus
)


class TestFilterCriterion:
    """Test the FilterCriterion model."""
    
    def test_creation(self):
        """Test creating a filter criterion."""
        criterion = FilterCriterion(
            field=FilterField.SENDER,
            operator=FilterOperator.CONTAINS,
            value="example.com"
        )
        
        assert criterion.field == FilterField.SENDER
        assert criterion.operator == FilterOperator.CONTAINS
        assert criterion.value == "example.com"
        assert criterion.case_sensitive is False
    
    def test_to_dict(self):
        """Test converting to dictionary."""
        criterion = FilterCriterion(
            field=FilterField.SUBJECT,
            operator=FilterOperator.REGEX,
            value="promo.*",
            case_sensitive=True
        )
        
        data = criterion.to_dict()
        expected = {
            "field": "subject",
            "operator": "regex",
            "value": "promo.*",
            "case_sensitive": True
        }
        
        assert data == expected
    
    def test_from_dict(self):
        """Test creating from dictionary."""
        data = {
            "field": "date",
            "operator": "greater_than",
            "value": "2023-01-01",
            "case_sensitive": False
        }
        
        criterion = FilterCriterion.from_dict(data)
        
        assert criterion.field == FilterField.DATE
        assert criterion.operator == FilterOperator.GREATER_THAN
        assert criterion.value == "2023-01-01"
        assert criterion.case_sensitive is False


class TestFilterRule:
    """Test the FilterRule model."""
    
    def test_creation(self):
        """Test creating a filter rule."""
        criteria = [
            FilterCriterion(FilterField.SENDER, FilterOperator.CONTAINS, "promo"),
            FilterCriterion(FilterField.SUBJECT, FilterOperator.CONTAINS, "sale")
        ]
        
        rule = FilterRule(
            criteria=criteria,
            logic="OR",
            name="Promotional emails",
            description="Emails from promotions or with sale subjects"
        )
        
        assert len(rule.criteria) == 2
        assert rule.logic == "OR"
        assert rule.name == "Promotional emails"
    
    def test_serialization(self):
        """Test rule serialization and deserialization."""
        criteria = [
            FilterCriterion(FilterField.SIZE, FilterOperator.GREATER_THAN, 1000000)
        ]
        
        rule = FilterRule(criteria=criteria, name="Large emails")
        
        # Convert to dict and back
        data = rule.to_dict()
        restored_rule = FilterRule.from_dict(data)
        
        assert restored_rule.name == rule.name
        assert len(restored_rule.criteria) == len(rule.criteria)
        assert restored_rule.criteria[0].field == rule.criteria[0].field


class TestSafetySettings:
    """Test the SafetySettings model."""
    
    def test_defaults(self):
        """Test default safety settings."""
        settings = SafetySettings()
        
        assert settings.skip_starred is True
        assert settings.skip_important is True
        assert settings.skip_from_contacts is True
        assert settings.thread_mode == "message"
        assert "invoice" in settings.protected_terms
        assert "receipt" in settings.protected_terms
    
    def test_serialization(self):
        """Test safety settings serialization."""
        settings = SafetySettings(
            skip_starred=False,
            protected_terms=["custom", "terms"],
            allowlist_domains=["trusted.com"]
        )
        
        data = settings.to_dict()
        restored_settings = SafetySettings.from_dict(data)
        
        assert restored_settings.skip_starred is False
        assert restored_settings.protected_terms == ["custom", "terms"]
        assert restored_settings.allowlist_domains == ["trusted.com"]


class TestEmailMessage:
    """Test the EmailMessage model."""
    
    def test_creation(self):
        """Test creating an email message."""
        message = EmailMessage(
            message_id="msg123",
            thread_id="thread456",
            subject="Test Subject",
            sender="<EMAIL>",
            date=datetime(2024, 1, 15, 10, 30),
            size_bytes=1024,
            labels=["INBOX", "CATEGORY_PROMOTIONS"],
            category="promotions"
        )
        
        assert message.message_id == "msg123"
        assert message.subject == "Test Subject"
        assert message.size_bytes == 1024
        assert "INBOX" in message.labels
    
    def test_serialization(self):
        """Test message serialization."""
        message = EmailMessage(
            message_id="msg123",
            thread_id="thread456",
            subject="Test",
            sender="<EMAIL>",
            date=datetime(2024, 1, 15),
            size_bytes=500
        )
        
        data = message.to_dict()
        restored_message = EmailMessage.from_dict(data)
        
        assert restored_message.message_id == message.message_id
        assert restored_message.subject == message.subject
        assert restored_message.date == message.date


class TestCleanupProfile:
    """Test the CleanupProfile model."""
    
    def test_creation(self):
        """Test creating a cleanup profile."""
        profile = CleanupProfile(
            name="Test Profile",
            description="A test profile",
            action=CleanupAction.ARCHIVE
        )
        
        assert profile.name == "Test Profile"
        assert profile.action == CleanupAction.ARCHIVE
        assert isinstance(profile.safety_settings, SafetySettings)
        assert isinstance(profile.created_at, datetime)
    
    def test_full_serialization(self):
        """Test complete profile serialization."""
        # Create a complex profile
        criteria = [
            FilterCriterion(FilterField.CATEGORY, FilterOperator.EQUALS, "promotions")
        ]
        rule = FilterRule(criteria=criteria, name="Promo rule")
        safety = SafetySettings(skip_starred=False)
        
        profile = CleanupProfile(
            name="Complex Profile",
            rules=[rule],
            safety_settings=safety,
            action=CleanupAction.TRASH,
            size_filter_mb=5.0
        )
        
        # Serialize and deserialize
        data = profile.to_dict()
        restored_profile = CleanupProfile.from_dict(data)
        
        assert restored_profile.name == profile.name
        assert len(restored_profile.rules) == 1
        assert restored_profile.rules[0].name == "Promo rule"
        assert restored_profile.safety_settings.skip_starred is False
        assert restored_profile.action == CleanupAction.TRASH
        assert restored_profile.size_filter_mb == 5.0
