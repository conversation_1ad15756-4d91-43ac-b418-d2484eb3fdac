"""
Preview tab for the Gmail Cleanup Tool.

Shows preview of messages that would be affected by cleanup operations.
"""

import logging
from typing import Optional

from PySide6.QtWidgets import Q<PERSON>abel, QWidget
from PySide6.QtGui import QFont

from .base_tab import BaseTab
from ...core.config import AppConfig

logger = logging.getLogger(__name__)


class PreviewTab(BaseTab):
    """Preview tab for showing cleanup candidates."""
    
    def __init__(self, config: AppConfig, parent: Optional[QWidget] = None):
        super().__init__(config, parent)
    
    def _setup_ui(self) -> None:
        """Set up the preview UI."""
        title = QLabel("Preview")
        title.setFont(QFont("Arial", 20, QFont.Bold))
        self.layout.addWidget(title)
        
        # TODO: Implement preview interface
        placeholder = QLabel("Preview interface will be implemented here.")
        self.layout.addWidget(placeholder)
        
        self.layout.addStretch()
