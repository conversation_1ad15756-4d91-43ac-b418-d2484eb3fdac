"""
Preview tab for the Gmail Cleanup Tool.

Shows preview of messages that would be affected by cleanup operations.
"""

import logging
from typing import Optional, List, Dict, Any
from datetime import datetime

from PySide6.QtWidgets import (
    QLabel, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QComboBox, QLineEdit,
    QProgressBar, QTextEdit, QSplitter, QFrame, QCheckBox, QSpinBox,
    QMessageBox, QFileDialog, QMenu
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont, QAction

from .base_tab import BaseTab
from ...core.config import AppConfig
from ...data.models import CleanupProfile, CleanupCandidate, CleanupRun, RunStatus
from ...data.database import DatabaseManager
from ...auth.oauth_manager import OAuthManager
from ...api.gmail_client import <PERSON><PERSON><PERSON>lient
from ...engine.cleanup_processor import CleanupProcessor

logger = logging.getLogger(__name__)


class PreviewWorker(QThread):
    """Worker thread for running preview operations."""

    progress_updated = Signal(str, int, int)  # message, current, total
    status_updated = Signal(str)
    preview_completed = Signal(object)  # CleanupRun
    error_occurred = Signal(str)

    def __init__(self, processor: CleanupProcessor, profile: CleanupProfile, max_messages: int):
        super().__init__()
        self.processor = processor
        self.profile = profile
        self.max_messages = max_messages

    def run(self):
        """Run preview in background thread."""
        try:
            # Set up callbacks
            self.processor.set_progress_callback(self.progress_updated.emit)
            self.processor.set_status_callback(self.status_updated.emit)

            # Run preview
            run = self.processor.preview_cleanup(self.profile, self.max_messages)
            self.preview_completed.emit(run)

        except Exception as e:
            logger.error(f"Preview worker error: {e}")
            self.error_occurred.emit(str(e))


class PreviewTab(BaseTab):
    """Preview tab for showing cleanup candidates."""

    def __init__(self, config: AppConfig, parent: Optional[QWidget] = None):
        # Initialize attributes first
        self.database = DatabaseManager(config)
        self.oauth_manager = OAuthManager(config)
        self.gmail_client = None
        self.processor = None

        self.current_run: Optional[CleanupRun] = None
        self.candidates: List[CleanupCandidate] = []
        self.filtered_candidates: List[CleanupCandidate] = []

        self.preview_worker = None

        # Then call parent constructor
        super().__init__(config, parent)

    def _setup_ui(self) -> None:
        """Set up the preview UI."""
        # Title
        title = QLabel("Preview")
        title.setFont(QFont("Arial", 20, QFont.Bold))
        title.setStyleSheet("color: #333; margin-bottom: 10px;")
        self.layout.addWidget(title)

        # Control section
        self._create_control_section()

        # Main content splitter
        splitter = QSplitter(Qt.Vertical)

        # Results table
        self._create_results_section(splitter)

        # Summary section
        self._create_summary_section(splitter)

        splitter.setSizes([400, 200])
        self.layout.addWidget(splitter)

    def _create_control_section(self) -> None:
        """Create the control section."""
        group = QGroupBox("Preview Controls")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QVBoxLayout(group)

        # Profile selection and run controls
        control_layout = QHBoxLayout()

        control_layout.addWidget(QLabel("Profile:"))
        self.profile_combo = QComboBox()
        self.profile_combo.setMinimumWidth(200)
        control_layout.addWidget(self.profile_combo)

        control_layout.addWidget(QLabel("Max messages:"))
        self.max_messages_spin = QSpinBox()
        self.max_messages_spin.setRange(100, 10000)
        self.max_messages_spin.setValue(1000)
        control_layout.addWidget(self.max_messages_spin)

        self.run_preview_btn = QPushButton("🔍 Run Preview")
        self.run_preview_btn.clicked.connect(self._run_preview)
        control_layout.addWidget(self.run_preview_btn)

        self.cancel_btn = QPushButton("❌ Cancel")
        self.cancel_btn.clicked.connect(self._cancel_preview)
        self.cancel_btn.setEnabled(False)
        control_layout.addWidget(self.cancel_btn)

        control_layout.addStretch()
        layout.addLayout(control_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # Status label
        self.status_label = QLabel("Ready to run preview")
        layout.addWidget(self.status_label)

        self.layout.addWidget(group)

        # Load profiles
        self._refresh_profiles()

    def _create_results_section(self, parent) -> None:
        """Create the results table section."""
        group = QGroupBox("Preview Results")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QVBoxLayout(group)

        # Filter controls
        filter_layout = QHBoxLayout()

        filter_layout.addWidget(QLabel("Filter:"))
        self.filter_combo = QComboBox()
        self.filter_combo.addItems([
            "All candidates", "Excluded only", "Active only",
            "By category", "By sender domain", "By size"
        ])
        self.filter_combo.currentTextChanged.connect(self._apply_filter)
        filter_layout.addWidget(self.filter_combo)

        self.filter_value_edit = QLineEdit()
        self.filter_value_edit.setPlaceholderText("Filter value...")
        self.filter_value_edit.textChanged.connect(self._apply_filter)
        filter_layout.addWidget(self.filter_value_edit)

        filter_layout.addStretch()

        # Export buttons
        export_csv_btn = QPushButton("📊 Export CSV")
        export_csv_btn.clicked.connect(self._export_csv)
        filter_layout.addWidget(export_csv_btn)

        export_eml_btn = QPushButton("📧 Export EML")
        export_eml_btn.clicked.connect(self._export_eml)
        filter_layout.addWidget(export_eml_btn)

        layout.addLayout(filter_layout)

        # Results table
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(8)
        self.results_table.setHorizontalHeaderLabels([
            "Date", "From", "Subject", "Size", "Category", "Labels", "Reasons", "Status"
        ])

        # Configure table
        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Date
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # From
        header.setSectionResizeMode(2, QHeaderView.Stretch)           # Subject
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Size
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Category
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Labels
        header.setSectionResizeMode(6, QHeaderView.Stretch)           # Reasons
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # Status

        self.results_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSortingEnabled(True)

        # Context menu
        self.results_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.results_table.customContextMenuRequested.connect(self._show_context_menu)

        layout.addWidget(self.results_table)

        parent.addWidget(group)

    def _create_summary_section(self, parent) -> None:
        """Create the summary section."""
        group = QGroupBox("Summary")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QHBoxLayout(group)

        # Statistics
        stats_layout = QVBoxLayout()

        self.total_label = QLabel("Total candidates: 0")
        stats_layout.addWidget(self.total_label)

        self.active_label = QLabel("Active candidates: 0")
        stats_layout.addWidget(self.active_label)

        self.excluded_label = QLabel("Excluded candidates: 0")
        stats_layout.addWidget(self.excluded_label)

        self.space_label = QLabel("Estimated space: 0 MB")
        stats_layout.addWidget(self.space_label)

        layout.addLayout(stats_layout)

        # Category breakdown
        self.category_text = QTextEdit()
        self.category_text.setMaximumHeight(100)
        self.category_text.setReadOnly(True)
        layout.addWidget(self.category_text)

        # Action buttons
        action_layout = QVBoxLayout()

        self.proceed_btn = QPushButton("▶️ Proceed to Cleanup")
        self.proceed_btn.clicked.connect(self._proceed_to_cleanup)
        self.proceed_btn.setEnabled(False)
        action_layout.addWidget(self.proceed_btn)

        self.save_results_btn = QPushButton("💾 Save Results")
        self.save_results_btn.clicked.connect(self._save_results)
        self.save_results_btn.setEnabled(False)
        action_layout.addWidget(self.save_results_btn)

        action_layout.addStretch()
        layout.addLayout(action_layout)

        parent.addWidget(group)

    def _refresh_profiles(self) -> None:
        """Refresh the profile list."""
        self.profile_combo.clear()
        self.profile_combo.addItem("-- Select Profile --")

        profiles = self.database.list_profiles()
        for profile in profiles:
            self.profile_combo.addItem(profile.name)

    def _run_preview(self) -> None:
        """Run preview operation."""
        if not self.require_authentication():
            return

        profile_name = self.profile_combo.currentText()
        if profile_name == "-- Select Profile --":
            self.emit_warning("No Profile", "Please select a profile to preview.")
            return

        profile = self.database.load_profile(profile_name)
        if not profile:
            self.emit_error("Profile Error", f"Could not load profile '{profile_name}'.")
            return

        if not profile.rules or not profile.rules[0].criteria:
            self.emit_warning("No Rules", "The selected profile has no filter rules.")
            return

        try:
            # Initialize Gmail client and processor
            if not self.gmail_client:
                self.gmail_client = GmailClient(self.oauth_manager)

            if not self.processor:
                self.processor = CleanupProcessor(self.gmail_client, self.database)

            # Start preview in background
            max_messages = self.max_messages_spin.value()

            self.preview_worker = PreviewWorker(self.processor, profile, max_messages)
            self.preview_worker.progress_updated.connect(self._on_progress_updated)
            self.preview_worker.status_updated.connect(self._on_status_updated)
            self.preview_worker.preview_completed.connect(self._on_preview_completed)
            self.preview_worker.error_occurred.connect(self._on_preview_error)

            # Update UI for running state
            self.run_preview_btn.setEnabled(False)
            self.cancel_btn.setEnabled(True)
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # Indeterminate

            self.preview_worker.start()

        except Exception as e:
            self.emit_error("Preview Error", f"Failed to start preview: {e}")

    def _cancel_preview(self) -> None:
        """Cancel the running preview."""
        if self.processor:
            self.processor.cancel_operation()

        if self.preview_worker and self.preview_worker.isRunning():
            self.preview_worker.terminate()
            self.preview_worker.wait(3000)  # Wait up to 3 seconds

        self._reset_ui_state()
        self.emit_status("Preview cancelled")

    def _on_progress_updated(self, message: str, current: int, total: int) -> None:
        """Handle progress updates."""
        if total > 0:
            self.progress_bar.setRange(0, total)
            self.progress_bar.setValue(current)

        self.status_label.setText(f"{message} ({current}/{total})")

    def _on_status_updated(self, status: str) -> None:
        """Handle status updates."""
        self.status_label.setText(status)
        self.emit_status(status)

    def _on_preview_completed(self, run: CleanupRun) -> None:
        """Handle preview completion."""
        self.current_run = run

        # Load candidates from database
        self.candidates = self.database.get_run_candidates(run.run_id)
        self.filtered_candidates = self.candidates.copy()

        # Update UI
        self._populate_results_table()
        self._update_summary()
        self._reset_ui_state()

        # Enable action buttons
        self.proceed_btn.setEnabled(True)
        self.save_results_btn.setEnabled(True)

        self.emit_status(f"Preview completed: {len([c for c in self.candidates if not c.excluded])} candidates found")

    def _on_preview_error(self, error: str) -> None:
        """Handle preview errors."""
        self._reset_ui_state()
        self.emit_error("Preview Failed", error)

    def _reset_ui_state(self) -> None:
        """Reset UI to ready state."""
        self.run_preview_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)

    def _populate_results_table(self) -> None:
        """Populate the results table with candidates."""
        self.results_table.setRowCount(len(self.filtered_candidates))

        for row, candidate in enumerate(self.filtered_candidates):
            message = candidate.message

            # Date
            date_item = QTableWidgetItem(message.date.strftime("%Y-%m-%d %H:%M"))
            self.results_table.setItem(row, 0, date_item)

            # From
            from_item = QTableWidgetItem(message.sender)
            self.results_table.setItem(row, 1, from_item)

            # Subject
            subject_item = QTableWidgetItem(message.subject)
            self.results_table.setItem(row, 2, subject_item)

            # Size
            size_mb = message.size_bytes / (1024 * 1024)
            size_item = QTableWidgetItem(f"{size_mb:.1f} MB")
            self.results_table.setItem(row, 3, size_item)

            # Category
            category_item = QTableWidgetItem(message.category or "None")
            self.results_table.setItem(row, 4, category_item)

            # Labels
            labels_item = QTableWidgetItem(", ".join(message.labels[:3]))  # Show first 3 labels
            self.results_table.setItem(row, 5, labels_item)

            # Reasons
            reasons_item = QTableWidgetItem("; ".join(candidate.reasons))
            self.results_table.setItem(row, 6, reasons_item)

            # Status
            status = "Excluded" if candidate.excluded else "Active"
            status_item = QTableWidgetItem(status)
            if candidate.excluded:
                status_item.setBackground(Qt.yellow)
            else:
                status_item.setBackground(Qt.lightGreen)
            self.results_table.setItem(row, 7, status_item)

    def _update_summary(self) -> None:
        """Update the summary section."""
        total_candidates = len(self.candidates)
        active_candidates = [c for c in self.candidates if not c.excluded]
        excluded_candidates = [c for c in self.candidates if c.excluded]

        # Update labels
        self.total_label.setText(f"Total candidates: {total_candidates:,}")
        self.active_label.setText(f"Active candidates: {len(active_candidates):,}")
        self.excluded_label.setText(f"Excluded candidates: {len(excluded_candidates):,}")

        # Calculate space
        total_space = sum(c.message.size_bytes for c in active_candidates) / (1024 * 1024)
        self.space_label.setText(f"Estimated space: {total_space:.1f} MB")

        # Category breakdown
        category_counts = {}
        for candidate in active_candidates:
            category = candidate.message.category or "None"
            category_counts[category] = category_counts.get(category, 0) + 1

        breakdown_text = "Category breakdown:\n"
        for category, count in sorted(category_counts.items()):
            breakdown_text += f"• {category}: {count:,} messages\n"

        self.category_text.setPlainText(breakdown_text)

    def _apply_filter(self) -> None:
        """Apply filter to the results."""
        filter_type = self.filter_combo.currentText()
        filter_value = self.filter_value_edit.text().lower()

        if filter_type == "All candidates":
            self.filtered_candidates = self.candidates.copy()
        elif filter_type == "Excluded only":
            self.filtered_candidates = [c for c in self.candidates if c.excluded]
        elif filter_type == "Active only":
            self.filtered_candidates = [c for c in self.candidates if not c.excluded]
        elif filter_type == "By category" and filter_value:
            self.filtered_candidates = [
                c for c in self.candidates
                if c.message.category and filter_value in c.message.category.lower()
            ]
        elif filter_type == "By sender domain" and filter_value:
            self.filtered_candidates = [
                c for c in self.candidates
                if filter_value in c.message.sender.lower()
            ]
        elif filter_type == "By size" and filter_value:
            try:
                size_mb = float(filter_value)
                size_bytes = size_mb * 1024 * 1024
                self.filtered_candidates = [
                    c for c in self.candidates
                    if c.message.size_bytes >= size_bytes
                ]
            except ValueError:
                self.filtered_candidates = self.candidates.copy()
        else:
            self.filtered_candidates = self.candidates.copy()

        self._populate_results_table()

    def _show_context_menu(self, position) -> None:
        """Show context menu for table items."""
        if self.results_table.itemAt(position) is None:
            return

        menu = QMenu(self)

        # Get selected row
        row = self.results_table.currentRow()
        if row < 0 or row >= len(self.filtered_candidates):
            return

        candidate = self.filtered_candidates[row]

        # Toggle exclusion
        if candidate.excluded:
            action = QAction("Include in cleanup", self)
            action.triggered.connect(lambda: self._toggle_exclusion(row, False))
        else:
            action = QAction("Exclude from cleanup", self)
            action.triggered.connect(lambda: self._toggle_exclusion(row, True))
        menu.addAction(action)

        menu.addSeparator()

        # Open in Gmail
        open_action = QAction("Open in Gmail", self)
        open_action.triggered.connect(lambda: self._open_in_gmail(candidate))
        menu.addAction(open_action)

        menu.exec(self.results_table.mapToGlobal(position))

    def _toggle_exclusion(self, row: int, exclude: bool) -> None:
        """Toggle exclusion status of a candidate."""
        if row < 0 or row >= len(self.filtered_candidates):
            return

        candidate = self.filtered_candidates[row]
        candidate.excluded = exclude

        # Update the table item
        status = "Excluded" if exclude else "Active"
        status_item = QTableWidgetItem(status)
        if exclude:
            status_item.setBackground(Qt.yellow)
        else:
            status_item.setBackground(Qt.lightGreen)
        self.results_table.setItem(row, 7, status_item)

        # Update summary
        self._update_summary()

    def _open_in_gmail(self, candidate: CleanupCandidate) -> None:
        """Open message in Gmail web interface."""
        import webbrowser
        gmail_url = f"https://mail.google.com/mail/u/0/#all/{candidate.message.message_id}"
        webbrowser.open(gmail_url)

    def _export_csv(self) -> None:
        """Export results to CSV."""
        if not self.candidates:
            self.emit_warning("No Data", "No preview results to export.")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Export Preview Results",
            f"preview_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            "CSV Files (*.csv);;All Files (*)"
        )

        if file_path:
            try:
                import csv
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)

                    # Write header
                    writer.writerow([
                        "Date", "From", "Subject", "Size (MB)", "Category",
                        "Labels", "Reasons", "Status", "Message ID"
                    ])

                    # Write data
                    for candidate in self.filtered_candidates:
                        message = candidate.message
                        size_mb = message.size_bytes / (1024 * 1024)
                        status = "Excluded" if candidate.excluded else "Active"

                        writer.writerow([
                            message.date.strftime("%Y-%m-%d %H:%M:%S"),
                            message.sender,
                            message.subject,
                            f"{size_mb:.1f}",
                            message.category or "",
                            ", ".join(message.labels),
                            "; ".join(candidate.reasons),
                            status,
                            message.message_id
                        ])

                self.emit_info("Export Successful", f"Results exported to {file_path}")

            except Exception as e:
                self.emit_error("Export Failed", f"Failed to export CSV: {e}")

    def _export_eml(self) -> None:
        """Export selected messages as EML files."""
        # TODO: Implement EML export
        self.emit_info("EML Export", "EML export will be implemented in a future version.")

    def _save_results(self) -> None:
        """Save preview results to database."""
        if not self.current_run:
            return

        try:
            # Update candidates in database with any exclusion changes
            self.database.save_run_candidates(self.current_run.run_id, self.candidates)
            self.emit_info("Results Saved", "Preview results saved successfully.")

        except Exception as e:
            self.emit_error("Save Failed", f"Failed to save results: {e}")

    def _proceed_to_cleanup(self) -> None:
        """Proceed to cleanup tab with current results."""
        if not self.current_run or not self.candidates:
            self.emit_warning("No Results", "No preview results to proceed with.")
            return

        # Save any changes first
        self._save_results()

        # Switch to cleanup tab
        if self.main_window:
            self.main_window.switch_to_tab("cleanup")

            # Pass data to cleanup tab
            cleanup_tab = self.main_window.tabs.get("cleanup")
            if cleanup_tab and hasattr(cleanup_tab, 'load_preview_results'):
                cleanup_tab.load_preview_results(self.current_run, self.candidates)

    def on_tab_activated(self) -> None:
        """Called when tab becomes active."""
        super().on_tab_activated()
        self._refresh_profiles()
