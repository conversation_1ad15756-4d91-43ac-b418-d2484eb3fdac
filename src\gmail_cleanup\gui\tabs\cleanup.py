"""
Cleanup tab for the Gmail Cleanup Tool.

Provides interface for executing cleanup operations with progress tracking.
"""

import logging
from typing import Optional, List, Dict, Any
from datetime import datetime

from PySide6.QtWidgets import (
    QLabel, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QPushButton,
    QProgressBar, QTextEdit, QSpinBox, QComboBox, QCheckBox, QFrame,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox, QLineEdit,
    QSplitter, QFormLayout, QScrollArea
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont, QPixmap

from .base_tab import BaseTab
from ...core.config import AppConfig
from ...data.models import CleanupProfile, CleanupCandidate, CleanupRun, CleanupAction, RunStatus
from ...data.database import DatabaseManager
from ...auth.oauth_manager import OAuthManager
from ...api.gmail_client import GmailClient
from ...engine.cleanup_processor import CleanupProcessor
from ...core.logging_config import log_user_action

logger = logging.getLogger(__name__)


class CleanupWorker(QThread):
    """Worker thread for running cleanup operations."""

    progress_updated = Signal(str, int, int)  # message, current, total
    status_updated = Signal(str)
    cleanup_completed = Signal(object)  # CleanupRun
    error_occurred = Signal(str)

    def __init__(self, processor: CleanupProcessor, profile: CleanupProfile,
                 candidates: List[CleanupCandidate], batch_size: int, delay_ms: int):
        super().__init__()
        self.processor = processor
        self.profile = profile
        self.candidates = candidates
        self.batch_size = batch_size
        self.delay_ms = delay_ms

    def run(self):
        """Run cleanup in background thread."""
        try:
            # Set up callbacks
            self.processor.set_progress_callback(self.progress_updated.emit)
            self.processor.set_status_callback(self.status_updated.emit)

            # Run cleanup
            run = self.processor.execute_cleanup(
                self.profile, self.candidates, self.batch_size, self.delay_ms
            )
            self.cleanup_completed.emit(run)

        except Exception as e:
            logger.error(f"Cleanup worker error: {e}")
            self.error_occurred.emit(str(e))


class ConfirmationDialog(QMessageBox):
    """Custom confirmation dialog for cleanup operations."""

    def __init__(self, action: CleanupAction, candidate_count: int,
                 estimated_space: float, parent=None):
        super().__init__(parent)

        self.setWindowTitle("Confirm Cleanup Operation")
        self.setIcon(QMessageBox.Warning if action == CleanupAction.DELETE_PERMANENT else QMessageBox.Question)

        # Build message based on action
        action_text = {
            CleanupAction.TRASH: "move to trash",
            CleanupAction.ARCHIVE: "archive",
            CleanupAction.LABEL_ONLY: "label",
            CleanupAction.DELETE_PERMANENT: "permanently delete"
        }.get(action, "process")

        message = f"""
You are about to {action_text} {candidate_count:,} messages.

Estimated space to be reclaimed: {estimated_space:.1f} MB

This action will:
"""

        if action == CleanupAction.TRASH:
            message += "• Move messages to Gmail Trash (recoverable for ~30 days)\n"
            message += "• Free up storage space in your Gmail account\n"
            message += "• Allow you to restore messages if needed"
        elif action == CleanupAction.ARCHIVE:
            message += "• Remove messages from your Inbox\n"
            message += "• Keep messages accessible via search\n"
            message += "• Preserve all message data"
        elif action == CleanupAction.LABEL_ONLY:
            message += "• Add a 'CLEANUP_PROCESSED' label to messages\n"
            message += "• Leave messages in their current location\n"
            message += "• Allow you to review the selection"
        elif action == CleanupAction.DELETE_PERMANENT:
            message += "• PERMANENTLY DELETE messages (CANNOT BE UNDONE)\n"
            message += "• Free up storage space immediately\n"
            message += "• Remove all message data forever"

        self.setText(message)

        if action == CleanupAction.DELETE_PERMANENT:
            self.setStandardButtons(QMessageBox.Cancel)
            # Add custom button for permanent delete
            delete_btn = self.addButton("I UNDERSTAND - DELETE PERMANENTLY", QMessageBox.AcceptRole)
            delete_btn.setStyleSheet("QPushButton { background-color: #d32f2f; color: white; font-weight: bold; }")
        else:
            self.setStandardButtons(QMessageBox.Yes | QMessageBox.Cancel)
            self.setDefaultButton(QMessageBox.Yes)


class CleanupTab(BaseTab):
    """Cleanup tab for executing cleanup operations."""

    def __init__(self, config: AppConfig, parent: Optional[QWidget] = None):
        # Initialize attributes first
        self.database = DatabaseManager(config)
        self.oauth_manager = OAuthManager(config)
        self.gmail_client = None
        self.processor = None

        self.current_profile: Optional[CleanupProfile] = None
        self.candidates: List[CleanupCandidate] = []
        self.preview_run: Optional[CleanupRun] = None

        self.cleanup_worker = None
        self.operation_in_progress = False

        # Then call parent constructor
        super().__init__(config, parent)

    def _setup_ui(self) -> None:
        """Set up the cleanup UI."""
        # Title
        title = QLabel("Cleanup Execution")
        title.setFont(QFont("Arial", 20, QFont.Bold))
        title.setStyleSheet("color: #333; margin-bottom: 10px;")
        self.layout.addWidget(title)

        # Main splitter
        splitter = QSplitter(Qt.Vertical)

        # Configuration section
        self._create_config_section(splitter)

        # Progress section
        self._create_progress_section(splitter)

        # Results section
        self._create_results_section(splitter)

        splitter.setSizes([200, 150, 250])
        self.layout.addWidget(splitter)

    def _create_config_section(self, parent) -> None:
        """Create the configuration section."""
        group = QGroupBox("Cleanup Configuration")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QVBoxLayout(group)

        # Status display
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.Box)
        status_frame.setStyleSheet("QFrame { background-color: #f0f0f0; padding: 10px; border-radius: 5px; }")
        status_layout = QVBoxLayout(status_frame)

        self.status_label = QLabel("No preview data loaded")
        self.status_label.setFont(QFont("Arial", 11, QFont.Bold))
        status_layout.addWidget(self.status_label)

        self.candidates_label = QLabel("Candidates: 0")
        status_layout.addWidget(self.candidates_label)

        self.space_label = QLabel("Estimated space: 0 MB")
        status_layout.addWidget(self.space_label)

        layout.addWidget(status_frame)

        # Action configuration
        config_layout = QFormLayout()

        # Action selector
        self.action_combo = QComboBox()
        self.action_combo.addItems([
            "Move to Trash (recommended)",
            "Archive (remove from Inbox)",
            "Label only (for testing)",
            "Permanent Delete (dangerous!)"
        ])
        self.action_combo.currentIndexChanged.connect(self._on_action_changed)
        config_layout.addRow("Action:", self.action_combo)

        # Batch configuration
        batch_layout = QHBoxLayout()

        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(10, 500)
        self.batch_size_spin.setValue(self.config.get("quotas.batch_size", 100))
        batch_layout.addWidget(self.batch_size_spin)

        batch_layout.addWidget(QLabel("messages per batch"))
        batch_layout.addStretch()

        config_layout.addRow("Batch size:", batch_layout)

        # Delay configuration
        delay_layout = QHBoxLayout()

        self.delay_spin = QSpinBox()
        self.delay_spin.setRange(100, 10000)
        self.delay_spin.setSuffix(" ms")
        self.delay_spin.setValue(self.config.get("quotas.per_batch_delay_ms", 1000))
        delay_layout.addWidget(self.delay_spin)

        delay_layout.addWidget(QLabel("delay between batches"))
        delay_layout.addStretch()

        config_layout.addRow("Delay:", delay_layout)

        layout.addLayout(config_layout)

        # Control buttons
        button_layout = QHBoxLayout()

        self.start_btn = QPushButton("🚀 Start Cleanup")
        self.start_btn.setMinimumHeight(40)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #1e7e34;
            }
            QPushButton:disabled {
                background-color: #ccc;
                color: #666;
            }
        """)
        self.start_btn.clicked.connect(self._start_cleanup)
        self.start_btn.setEnabled(False)
        button_layout.addWidget(self.start_btn)

        self.cancel_btn = QPushButton("❌ Cancel")
        self.cancel_btn.setMinimumHeight(40)
        self.cancel_btn.clicked.connect(self._cancel_cleanup)
        self.cancel_btn.setEnabled(False)
        button_layout.addWidget(self.cancel_btn)

        button_layout.addStretch()

        self.load_preview_btn = QPushButton("📋 Load Preview Results")
        self.load_preview_btn.clicked.connect(self._load_preview_results)
        button_layout.addWidget(self.load_preview_btn)

        layout.addLayout(button_layout)

        parent.addWidget(group)

    def _create_progress_section(self, parent) -> None:
        """Create the progress tracking section."""
        group = QGroupBox("Progress")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QVBoxLayout(group)

        # Overall progress
        self.overall_progress = QProgressBar()
        self.overall_progress.setVisible(False)
        layout.addWidget(self.overall_progress)

        # Current operation status
        self.operation_status = QLabel("Ready to start cleanup")
        layout.addWidget(self.operation_status)

        # Statistics during operation
        stats_layout = QHBoxLayout()

        self.processed_label = QLabel("Processed: 0")
        stats_layout.addWidget(self.processed_label)

        self.success_label = QLabel("Success: 0")
        stats_layout.addWidget(self.success_label)

        self.error_label = QLabel("Errors: 0")
        stats_layout.addWidget(self.error_label)

        self.eta_label = QLabel("ETA: --")
        stats_layout.addWidget(self.eta_label)

        stats_layout.addStretch()
        layout.addLayout(stats_layout)

        # Live log
        self.live_log = QTextEdit()
        self.live_log.setMaximumHeight(100)
        self.live_log.setReadOnly(True)
        self.live_log.setStyleSheet("QTextEdit { font-family: 'Courier New', monospace; font-size: 9pt; }")
        layout.addWidget(self.live_log)

        parent.addWidget(group)

    def _create_results_section(self, parent) -> None:
        """Create the results section."""
        group = QGroupBox("Results")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QVBoxLayout(group)

        # Results summary
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setPlainText("No cleanup operations completed yet.")
        layout.addWidget(self.results_text)

        # Action buttons for results
        results_button_layout = QHBoxLayout()

        self.view_report_btn = QPushButton("📊 View Detailed Report")
        self.view_report_btn.clicked.connect(self._view_detailed_report)
        self.view_report_btn.setEnabled(False)
        results_button_layout.addWidget(self.view_report_btn)

        self.undo_btn = QPushButton("↶ Undo (Restore from Trash)")
        self.undo_btn.clicked.connect(self._undo_cleanup)
        self.undo_btn.setEnabled(False)
        results_button_layout.addWidget(self.undo_btn)

        self.export_report_btn = QPushButton("💾 Export Report")
        self.export_report_btn.clicked.connect(self._export_report)
        self.export_report_btn.setEnabled(False)
        results_button_layout.addWidget(self.export_report_btn)

        results_button_layout.addStretch()
        layout.addLayout(results_button_layout)

        parent.addWidget(group)

    def load_preview_results(self, preview_run: CleanupRun, candidates: List[CleanupCandidate]) -> None:
        """Load preview results for cleanup execution."""
        self.preview_run = preview_run
        self.candidates = candidates

        # Load the profile
        if preview_run.profile_name:
            self.current_profile = self.database.load_profile(preview_run.profile_name)

        # Update UI
        self._update_status_display()
        self.start_btn.setEnabled(True)

        # Set action from profile
        if self.current_profile:
            action_map = {
                CleanupAction.TRASH: 0,
                CleanupAction.ARCHIVE: 1,
                CleanupAction.LABEL_ONLY: 2,
                CleanupAction.DELETE_PERMANENT: 3
            }
            self.action_combo.setCurrentIndex(action_map.get(self.current_profile.action, 0))

        self.emit_status(f"Loaded preview results: {len([c for c in candidates if not c.excluded])} candidates ready")

    def _update_status_display(self) -> None:
        """Update the status display."""
        if not self.candidates:
            self.status_label.setText("No preview data loaded")
            self.candidates_label.setText("Candidates: 0")
            self.space_label.setText("Estimated space: 0 MB")
            return

        active_candidates = [c for c in self.candidates if not c.excluded]
        total_space = sum(c.message.size_bytes for c in active_candidates) / (1024 * 1024)

        profile_name = self.current_profile.name if self.current_profile else "Unknown"
        self.status_label.setText(f"Profile: {profile_name}")
        self.candidates_label.setText(f"Candidates: {len(active_candidates):,}")
        self.space_label.setText(f"Estimated space: {total_space:.1f} MB")

    def _on_action_changed(self) -> None:
        """Handle action selection change."""
        action_index = self.action_combo.currentIndex()

        # Update button styling based on action danger level
        if action_index == 3:  # Permanent delete
            self.start_btn.setStyleSheet("""
                QPushButton {
                    background-color: #d32f2f;
                    color: white;
                    font-weight: bold;
                    border: none;
                    border-radius: 5px;
                    padding: 10px 20px;
                }
                QPushButton:hover {
                    background-color: #b71c1c;
                }
                QPushButton:disabled {
                    background-color: #ccc;
                    color: #666;
                }
            """)
            self.start_btn.setText("⚠️ PERMANENT DELETE")
        else:
            self.start_btn.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    font-weight: bold;
                    border: none;
                    border-radius: 5px;
                    padding: 10px 20px;
                }
                QPushButton:hover {
                    background-color: #1e7e34;
                }
                QPushButton:disabled {
                    background-color: #ccc;
                    color: #666;
                }
            """)
            self.start_btn.setText("🚀 Start Cleanup")

    def _start_cleanup(self) -> None:
        """Start the cleanup operation."""
        if not self.require_authentication():
            return

        if not self.candidates:
            self.emit_warning("No Data", "No preview results loaded. Please run a preview first.")
            return

        active_candidates = [c for c in self.candidates if not c.excluded]
        if not active_candidates:
            self.emit_warning("No Candidates", "No active candidates to process.")
            return

        # Get selected action
        action_map = {
            0: CleanupAction.TRASH,
            1: CleanupAction.ARCHIVE,
            2: CleanupAction.LABEL_ONLY,
            3: CleanupAction.DELETE_PERMANENT
        }
        action = action_map[self.action_combo.currentIndex()]

        # Calculate estimated space
        estimated_space = sum(c.message.size_bytes for c in active_candidates) / (1024 * 1024)

        # Show confirmation dialog
        dialog = ConfirmationDialog(action, len(active_candidates), estimated_space, self)
        if dialog.exec() != QMessageBox.AcceptRole:
            return

        # Additional confirmation for permanent delete
        if action == CleanupAction.DELETE_PERMANENT:
            confirmation_text, ok = QLineEdit.getText(
                self,
                "Final Confirmation",
                f"Type 'PERMANENTLY DELETE {len(active_candidates)} MESSAGES' to confirm:",
                QLineEdit.Normal,
                ""
            )

            expected_text = f"PERMANENTLY DELETE {len(active_candidates)} MESSAGES"
            if not ok or confirmation_text != expected_text:
                self.emit_info("Cancelled", "Permanent delete operation cancelled.")
                return

        try:
            # Update profile action
            if self.current_profile:
                self.current_profile.action = action

            # Initialize Gmail client and processor
            if not self.gmail_client:
                self.gmail_client = GmailClient(self.oauth_manager)

            if not self.processor:
                self.processor = CleanupProcessor(self.gmail_client, self.database)

            # Start cleanup in background
            batch_size = self.batch_size_spin.value()
            delay_ms = self.delay_spin.value()

            self.cleanup_worker = CleanupWorker(
                self.processor, self.current_profile, active_candidates, batch_size, delay_ms
            )
            self.cleanup_worker.progress_updated.connect(self._on_progress_updated)
            self.cleanup_worker.status_updated.connect(self._on_status_updated)
            self.cleanup_worker.cleanup_completed.connect(self._on_cleanup_completed)
            self.cleanup_worker.error_occurred.connect(self._on_cleanup_error)

            # Update UI for running state
            self.operation_in_progress = True
            self.start_btn.setEnabled(False)
            self.cancel_btn.setEnabled(True)
            self.overall_progress.setVisible(True)
            self.overall_progress.setRange(0, len(active_candidates))
            self.overall_progress.setValue(0)

            # Clear previous results
            self.live_log.clear()
            self.results_text.clear()

            # Start operation
            self.cleanup_worker.start()

            log_user_action("cleanup_started", {
                "action": action.value,
                "candidates": len(active_candidates),
                "batch_size": batch_size,
                "delay_ms": delay_ms
            })

        except Exception as e:
            self.emit_error("Cleanup Error", f"Failed to start cleanup: {e}")
            self._reset_ui_state()

    def _cancel_cleanup(self) -> None:
        """Cancel the running cleanup operation."""
        if self.processor:
            self.processor.cancel_operation()

        if self.cleanup_worker and self.cleanup_worker.isRunning():
            self.cleanup_worker.terminate()
            self.cleanup_worker.wait(3000)  # Wait up to 3 seconds

        self._reset_ui_state()
        self.emit_status("Cleanup operation cancelled")
        self._log_message("Operation cancelled by user")

    def _on_progress_updated(self, message: str, current: int, total: int) -> None:
        """Handle progress updates."""
        self.overall_progress.setMaximum(total)
        self.overall_progress.setValue(current)

        self.processed_label.setText(f"Processed: {current:,}")
        self.operation_status.setText(f"{message} ({current}/{total})")

        # Calculate ETA
        if current > 0 and hasattr(self, '_start_time'):
            elapsed = datetime.now() - self._start_time
            rate = current / elapsed.total_seconds()
            if rate > 0:
                remaining_seconds = (total - current) / rate
                eta_minutes = int(remaining_seconds / 60)
                eta_seconds = int(remaining_seconds % 60)
                self.eta_label.setText(f"ETA: {eta_minutes}m {eta_seconds}s")

        self._log_message(f"Progress: {current}/{total} - {message}")

    def _on_status_updated(self, status: str) -> None:
        """Handle status updates."""
        self.operation_status.setText(status)
        self.emit_status(status)
        self._log_message(status)

    def _on_cleanup_completed(self, run: CleanupRun) -> None:
        """Handle cleanup completion."""
        self.current_run = run

        # Update UI
        self._reset_ui_state()

        # Update statistics
        self.success_label.setText(f"Success: {run.success_count:,}")
        self.error_label.setText(f"Errors: {run.error_count:,}")

        # Show results
        self._display_results(run)

        # Enable result buttons
        self.view_report_btn.setEnabled(True)
        self.export_report_btn.setEnabled(True)

        # Enable undo only for trash operations
        if run.action == CleanupAction.TRASH:
            self.undo_btn.setEnabled(True)

        self.emit_status(f"Cleanup completed: {run.success_count} processed, {run.error_count} errors")

        log_user_action("cleanup_completed", {
            "run_id": run.run_id,
            "success_count": run.success_count,
            "error_count": run.error_count,
            "actual_space_mb": run.actual_space_mb
        })

    def _on_cleanup_error(self, error: str) -> None:
        """Handle cleanup errors."""
        self._reset_ui_state()
        self.emit_error("Cleanup Failed", error)
        self._log_message(f"ERROR: {error}")

    def _reset_ui_state(self) -> None:
        """Reset UI to ready state."""
        self.operation_in_progress = False
        self.start_btn.setEnabled(bool(self.candidates))
        self.cancel_btn.setEnabled(False)
        self.overall_progress.setVisible(False)
        self.overall_progress.setValue(0)

        if hasattr(self, '_start_time'):
            delattr(self, '_start_time')

    def _log_message(self, message: str) -> None:
        """Add message to live log."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.live_log.append(f"[{timestamp}] {message}")

        # Auto-scroll to bottom
        scrollbar = self.live_log.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def _display_results(self, run: CleanupRun) -> None:
        """Display cleanup results."""
        action_names = {
            CleanupAction.TRASH: "moved to trash",
            CleanupAction.ARCHIVE: "archived",
            CleanupAction.LABEL_ONLY: "labeled",
            CleanupAction.DELETE_PERMANENT: "permanently deleted"
        }

        action_name = action_names.get(run.action, "processed")

        results_text = f"""
Cleanup Operation Completed
==========================

Profile: {run.profile_name}
Action: {run.action.value.replace('_', ' ').title()}
Started: {run.started_at.strftime('%Y-%m-%d %H:%M:%S')}
Completed: {run.completed_at.strftime('%Y-%m-%d %H:%M:%S') if run.completed_at else 'N/A'}
Duration: {(run.completed_at - run.started_at).total_seconds():.1f} seconds

Results:
--------
Total candidates: {run.total_candidates:,}
Successfully {action_name}: {run.success_count:,}
Errors encountered: {run.error_count:,}
Actual space reclaimed: {run.actual_space_mb:.1f} MB

Status: {run.status.value.replace('_', ' ').title()}
"""

        if run.error_messages:
            results_text += f"\nRecent Errors:\n"
            for error in run.error_messages[:5]:  # Show first 5 errors
                results_text += f"• {error}\n"

            if len(run.error_messages) > 5:
                results_text += f"... and {len(run.error_messages) - 5} more errors\n"

        if run.action == CleanupAction.TRASH:
            results_text += f"\nNote: Messages can be restored from Gmail Trash within ~30 days."
        elif run.action == CleanupAction.DELETE_PERMANENT:
            results_text += f"\nWarning: Messages have been permanently deleted and cannot be recovered."

        self.results_text.setPlainText(results_text)

    def _load_preview_results(self) -> None:
        """Load preview results from database."""
        # Get recent preview runs
        runs = self.database.list_runs(limit=10)
        preview_runs = [r for r in runs if r.status == RunStatus.COMPLETED and r.action == CleanupAction.TRASH]

        if not preview_runs:
            self.emit_warning("No Preview Data", "No recent preview runs found. Please run a preview first.")
            return

        # For now, load the most recent preview run
        # In a full implementation, we'd show a selection dialog
        latest_run = preview_runs[0]
        candidates = self.database.get_run_candidates(latest_run.run_id)

        self.load_preview_results(latest_run, candidates)
        self.emit_info("Preview Loaded", f"Loaded preview results from {latest_run.started_at.strftime('%Y-%m-%d %H:%M')}")

    def _view_detailed_report(self) -> None:
        """View detailed report in Backups & Reports tab."""
        if not self.current_run:
            return

        if self.main_window:
            self.main_window.switch_to_tab("backups")

            # Pass run data to backups tab
            backups_tab = self.main_window.tabs.get("backups")
            if backups_tab and hasattr(backups_tab, 'show_run_details'):
                backups_tab.show_run_details(self.current_run)

    def _undo_cleanup(self) -> None:
        """Undo the last cleanup operation by restoring from trash."""
        if not self.current_run or self.current_run.action != CleanupAction.TRASH:
            self.emit_warning("Cannot Undo", "Can only undo trash operations.")
            return

        reply = QMessageBox.question(
            self,
            "Undo Cleanup",
            f"This will restore {self.current_run.success_count:,} messages from Gmail Trash.\n\n"
            "Are you sure you want to continue?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # TODO: Implement undo functionality
            self.emit_info("Undo", "Undo functionality will be implemented in the Backups & Reports tab.")

    def _export_report(self) -> None:
        """Export cleanup report."""
        if not self.current_run:
            return

        from PySide6.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Export Cleanup Report",
            f"cleanup_report_{self.current_run.run_id[:8]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "Text Files (*.txt);;All Files (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.results_text.toPlainText())

                self.emit_info("Export Successful", f"Report exported to {file_path}")

            except Exception as e:
                self.emit_error("Export Failed", f"Failed to export report: {e}")

    def is_operation_in_progress(self) -> bool:
        """Check if a cleanup operation is currently in progress."""
        return self.operation_in_progress

    def on_tab_activated(self) -> None:
        """Called when tab becomes active."""
        super().on_tab_activated()
        self._update_status_display()
