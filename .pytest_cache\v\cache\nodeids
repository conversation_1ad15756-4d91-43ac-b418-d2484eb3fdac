["tests/test_config.py::TestAppConfig::test_config_get_set", "tests/test_config.py::TestAppConfig::test_config_initialization", "tests/test_config.py::TestAppConfig::test_config_persistence", "tests/test_config.py::TestAppConfig::test_database_path", "tests/test_config.py::TestAppConfig::test_directory_creation", "tests/test_models.py::TestCleanupProfile::test_creation", "tests/test_models.py::TestCleanupProfile::test_full_serialization", "tests/test_models.py::TestEmailMessage::test_creation", "tests/test_models.py::TestEmailMessage::test_serialization", "tests/test_models.py::TestFilterCriterion::test_creation", "tests/test_models.py::TestFilterCriterion::test_from_dict", "tests/test_models.py::TestFilterCriterion::test_to_dict", "tests/test_models.py::TestFilterRule::test_creation", "tests/test_models.py::TestFilterRule::test_serialization", "tests/test_models.py::TestSafetySettings::test_defaults", "tests/test_models.py::TestSafetySettings::test_serialization"]