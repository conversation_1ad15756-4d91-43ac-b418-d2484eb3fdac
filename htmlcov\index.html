<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">7%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-19 10:04 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_f4fb5d423fccdd22___init___py.html">src\gmail_cleanup\__init__.py</a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aa9d318d4a813f3c___init___py.html">src\gmail_cleanup\api\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aa9d318d4a813f3c_gmail_client_py.html">src\gmail_cleanup\api\gmail_client.py</a></td>
                <td>244</td>
                <td>244</td>
                <td>0</td>
                <td class="right" data-ratio="0 244">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf___init___py.html">src\gmail_cleanup\auth\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_oauth_manager_py.html">src\gmail_cleanup\auth\oauth_manager.py</a></td>
                <td>170</td>
                <td>170</td>
                <td>0</td>
                <td class="right" data-ratio="0 170">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a8d156ea6f187bf_token_storage_py.html">src\gmail_cleanup\auth\token_storage.py</a></td>
                <td>133</td>
                <td>133</td>
                <td>0</td>
                <td class="right" data-ratio="0 133">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc___init___py.html">src\gmail_cleanup\core\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_config_py.html">src\gmail_cleanup\core\config.py</a></td>
                <td>77</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="64 77">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_95d0aa1dabc85fcc_logging_config_py.html">src\gmail_cleanup\core\logging_config.py</a></td>
                <td>54</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="0 54">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece___init___py.html">src\gmail_cleanup\data\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_database_py.html">src\gmail_cleanup\data\database.py</a></td>
                <td>136</td>
                <td>136</td>
                <td>0</td>
                <td class="right" data-ratio="0 136">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_63ffc747e1711ece_models_py.html">src\gmail_cleanup\data\models.py</a></td>
                <td>139</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="135 139">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4672292787daf277___init___py.html">src\gmail_cleanup\engine\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4672292787daf277_cleanup_processor_py.html">src\gmail_cleanup\engine\cleanup_processor.py</a></td>
                <td>166</td>
                <td>166</td>
                <td>0</td>
                <td class="right" data-ratio="0 166">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4672292787daf277_rule_engine_py.html">src\gmail_cleanup\engine\rule_engine.py</a></td>
                <td>253</td>
                <td>253</td>
                <td>0</td>
                <td class="right" data-ratio="0 253">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3___init___py.html">src\gmail_cleanup\gui\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4d4dad31ad711f3_main_window_py.html">src\gmail_cleanup\gui\main_window.py</a></td>
                <td>117</td>
                <td>117</td>
                <td>0</td>
                <td class="right" data-ratio="0 117">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e___init___py.html">src\gmail_cleanup\gui\tabs\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_backups_reports_py.html">src\gmail_cleanup\gui\tabs\backups_reports.py</a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_base_tab_py.html">src\gmail_cleanup\gui\tabs\base_tab.py</a></td>
                <td>71</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="0 71">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_cleanup_py.html">src\gmail_cleanup\gui\tabs\cleanup.py</a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_criteria_builder_py.html">src\gmail_cleanup\gui\tabs\criteria_builder.py</a></td>
                <td>392</td>
                <td>392</td>
                <td>0</td>
                <td class="right" data-ratio="0 392">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_dashboard_py.html">src\gmail_cleanup\gui\tabs\dashboard.py</a></td>
                <td>168</td>
                <td>168</td>
                <td>0</td>
                <td class="right" data-ratio="0 168">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_logs_py.html">src\gmail_cleanup\gui\tabs\logs.py</a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_preview_py.html">src\gmail_cleanup\gui\tabs\preview.py</a></td>
                <td>370</td>
                <td>370</td>
                <td>0</td>
                <td class="right" data-ratio="0 370">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_699f37a4a3595e6e_settings_py.html">src\gmail_cleanup\gui\tabs\settings.py</a></td>
                <td>223</td>
                <td>223</td>
                <td>0</td>
                <td class="right" data-ratio="0 223">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f4fb5d423fccdd22_main_py.html">src\gmail_cleanup\main.py</a></td>
                <td>35</td>
                <td>35</td>
                <td>2</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>2808</td>
                <td>2602</td>
                <td>2</td>
                <td class="right" data-ratio="206 2808">7%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-19 10:04 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_f4fb5d423fccdd22_main_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_f4fb5d423fccdd22___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
